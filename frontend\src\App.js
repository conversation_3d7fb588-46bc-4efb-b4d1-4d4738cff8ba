import React, { useState, useCallback, useEffect } from 'react';
import { Container, Box, Typography } from '@mui/material';
import MainContent from './components/MainContent';
import Sidebar from './components/Sidebar';
import { correctText } from './services/apiService';
import { saveAs } from 'file-saver';
import Login from './components/Login';
import './App.css';

// Translation constants
const TRANSLATIONS = {
  ja: {
    errors: {
      emptyText: 'テキストを入力してください。',
      generalError: 'エラーが発生しました。'
    },
    labels: {
      processing: '処理中...',
      download: '訂正済み議事録'
    }
  },
  en: {
    errors: {
      emptyText: 'Please enter text.',
      generalError: 'An error occurred.'
    },
    labels: {
      processing: 'Processing...',
      download: 'Corrected Transcript'
    }
  }
};

function App() {
  const [originalText, setOriginalText] = useState('');
  const [correctedText, setCorrectedText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingInfo, setProcessingInfo] = useState(null);
  const [error, setError] = useState('');
  const [mode, setMode] = useState('proofreading');
  const [model, setModel] = useState('gpt-4');
  const [customPrompt, setCustomPrompt] = useState('');
  const [wordList, setWordList] = useState('');
  const [language, setLanguage] = useState('ja');
  const [sidebarWidth, setSidebarWidth] = useState(350);
  const [isResizing, setIsResizing] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  const [selectedMenu, setSelectedMenu] = useState('transcript');
  const [userRole, setUserRole] = useState('ADMIN'); // テスト用にADMINに変更
  const [adminSubMenu, setAdminSubMenu] = useState('overview');

  // 認証状態をlocalStorageから復元
  useEffect(() => {
    const checkAuthStatus = () => {
      try {
        const authToken = localStorage.getItem('authToken');
        const authExpiry = localStorage.getItem('authExpiry');

        if (authToken && authExpiry) {
          const expiryTime = new Date(authExpiry);
          const currentTime = new Date();

          if (currentTime < expiryTime) {
            setIsAuthenticated(true);
          } else {
            // トークンが期限切れの場合はクリア
            localStorage.removeItem('authToken');
            localStorage.removeItem('authExpiry');
            setIsAuthenticated(false);
          }
        } else {
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error('認証状態の確認中にエラーが発生しました:', error);
        setIsAuthenticated(false);
      } finally {
        setIsCheckingAuth(false);
      }
    };

    checkAuthStatus();
  }, []);

  // ドラッグでサイドバー幅を変更
  const handleMouseDown = () => {
    setIsResizing(true);
    document.body.style.cursor = 'col-resize';
  };
  const handleMouseMove = React.useCallback((e) => {
    if (isResizing) {
      const min = 180, max = 500;
      setSidebarWidth(Math.min(max, Math.max(min, e.clientX - 16)));
    }
  }, [isResizing]);
  const handleMouseUp = () => {
    setIsResizing(false);
    document.body.style.cursor = '';
  };
  useEffect(() => {
    if (isResizing) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    } else {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    }
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, handleMouseMove]);

  // テキスト校正処理
  const handleCorrectText = useCallback(async (originalText, mode, model, customPrompt, wordList) => {
    if (!originalText.trim()) {
      setError(TRANSLATIONS[language].errors.emptyText);
      return;
    }

    setError('');
    setIsProcessing(true);
    try {
      const response = await correctText(originalText, mode, model, customPrompt, wordList, language);
      setCorrectedText(response.correctedText);
      setProcessingInfo(response.processingInfo);
    } catch (err) {
      setError(err.message || TRANSLATIONS[language].errors.generalError);
    } finally {
      setIsProcessing(false);
    }
  }, [correctText, language]);

  // 結果のダウンロード
  const handleDownload = () => {
    const blob = new Blob([correctedText], { type: 'text/plain;charset=utf-8' });
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
    const prefix = TRANSLATIONS[language].labels.download;
    saveAs(blob, `${prefix}_${timestamp}.txt`);
  };

  // ログアウト処理
  const handleLogout = () => {
    try {
      // localStorageから認証情報を削除
      localStorage.removeItem('authToken');
      localStorage.removeItem('authExpiry');
      setIsAuthenticated(false);
      // 必要ならサーバー側でセッション削除APIを呼ぶ
    } catch (error) {
      console.error('ログアウト処理中にエラーが発生しました:', error);
      setIsAuthenticated(false);
    }
  };

  // ログイン成功時の処理
  const handleLogin = (token = null) => {
    try {
      if (token) {
        // トークンが提供された場合はlocalStorageに保存
        const expiryTime = new Date();
        expiryTime.setHours(expiryTime.getHours() + 24); // 24時間後に期限切れ

        localStorage.setItem('authToken', token);
        localStorage.setItem('authExpiry', expiryTime.toISOString());
      } else {
        // トークンが提供されない場合はデフォルトの認証状態を設定
        const expiryTime = new Date();
        expiryTime.setHours(expiryTime.getHours() + 24);

        localStorage.setItem('authToken', 'default-session');
        localStorage.setItem('authExpiry', expiryTime.toISOString());
      }
      setIsAuthenticated(true);
    } catch (error) {
      console.error('ログイン処理中にエラーが発生しました:', error);
    }
  };

  // 認証状態をチェック中の場合はローディング表示
  if (isCheckingAuth) {
    return (
      <Container maxWidth={false} sx={{ height: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Box sx={{ textAlign: 'center' }}>
          <Typography variant="h6">認証状態を確認中...</Typography>
        </Box>
      </Container>
    );
  }

  if (!isAuthenticated) {
    return <Login onLogin={handleLogin} />;
  }

  return (
    <Container maxWidth={false} sx={{ height: '100vh', py: 2 }}>
      <Box sx={{ height: '100%', display: 'flex', gap: 0 }}>
        <Box sx={{ width: sidebarWidth, flexShrink: 0, transition: 'width 0.1s' }}>
          <Sidebar
            mode={mode}
            setMode={setMode}
            model={model}
            setModel={setModel}
            customPrompt={customPrompt}
            setCustomPrompt={setCustomPrompt}
            wordList={wordList}
            setWordList={setWordList}
            handleCorrectText={handleCorrectText}
            originalText={originalText}
            setOriginalText={setOriginalText}
            isProcessing={isProcessing}
            language={language}
            setLanguage={setLanguage}
            onLogout={handleLogout}
            selectedMenu={selectedMenu}
            setSelectedMenu={setSelectedMenu}
            userRole={userRole}
            adminSubMenu={adminSubMenu}
            setAdminSubMenu={setAdminSubMenu}
          />
        </Box>
        {/* リサイズバー */}
        <Box
          sx={{ width: 6, cursor: 'col-resize', background: isResizing ? '#1976d2' : '#e0e0e0', zIndex: 10 }}
          onMouseDown={handleMouseDown}
        />
        <Box sx={{ flex: 1 }}>
          <MainContent
            originalText={originalText}
            correctedText={correctedText}
            setCorrectedText={setCorrectedText}
            isProcessing={isProcessing}
            handleCorrectText={handleCorrectText}
            handleDownload={handleDownload}
            processingInfo={processingInfo}
            error={error}
            language={language}
            selectedMenu={selectedMenu}
            userRole={userRole}
            adminSubMenu={adminSubMenu}
          />
        </Box>
      </Box>
    </Container>
  );
}

export default App;
