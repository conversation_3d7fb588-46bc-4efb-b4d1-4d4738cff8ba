-- TranscriptCleaner Role Management Schema
-- ロール管理機能用データベーススキーマ

-- ============================================================================
-- 1. role テーブル - ロール定義
-- ============================================================================
CREATE TABLE IF NOT EXISTS role (
    id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT 'ロールID',
    name VARCHAR(32) NOT NULL UNIQUE COMMENT 'ロール名（ADMIN, USER, GUEST等）',
    display_name VARCHAR(64) NOT NULL COMMENT 'ロール表示名',
    description TEXT COMMENT 'ロールの説明',
    permissions JSON COMMENT '権限設定（JSON形式）',
    is_active TINYINT(1) DEFAULT 1 COMMENT 'アクティブフラグ（1:有効, 0:無効）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '作成日時',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新日時',
    
    INDEX idx_name (name),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ロール定義テーブル';

-- ============================================================================
-- 2. user_role テーブル - ユーザーとロールの関連
-- ============================================================================
CREATE TABLE IF NOT EXISTS user_role (
    id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT 'ユーザーロールID',
    user_id BIGINT(20) NOT NULL COMMENT 'ユーザーID',
    role_id BIGINT(20) NOT NULL COMMENT 'ロールID',
    assigned_by BIGINT(20) COMMENT '割り当てを行ったユーザーID',
    assigned_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '割り当て日時',
    expires_at DATETIME COMMENT '有効期限（NULLの場合は無期限）',
    is_active TINYINT(1) DEFAULT 1 COMMENT 'アクティブフラグ（1:有効, 0:無効）',
    
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES role(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES user(id) ON DELETE SET NULL,
    
    UNIQUE KEY unique_user_role (user_id, role_id),
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id),
    INDEX idx_assigned_by (assigned_by),
    INDEX idx_is_active (is_active),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ユーザーロール関連テーブル';

-- ============================================================================
-- 3. デフォルトロールの挿入
-- ============================================================================

-- システム管理者ロール
INSERT IGNORE INTO role (id, name, display_name, description, permissions, is_active) VALUES 
(1, 'ADMIN', 'システム管理者', 'システム全体の管理権限を持つ', 
JSON_OBJECT(
    'user_management', true,
    'role_management', true,
    'system_settings', true,
    'view_all_data', true,
    'export_data', true,
    'delete_data', true,
    'cost_management', true,
    'api_key_management', true
), 1);

-- 一般ユーザーロール
INSERT IGNORE INTO role (id, name, display_name, description, permissions, is_active) VALUES 
(2, 'USER', '一般ユーザー', '基本的な機能を利用できる', 
JSON_OBJECT(
    'text_correction', true,
    'word_list_management', true,
    'view_own_history', true,
    'download_results', true,
    'profile_edit', true
), 1);

-- ゲストユーザーロール
INSERT IGNORE INTO role (id, name, display_name, description, permissions, is_active) VALUES 
(3, 'GUEST', 'ゲストユーザー', '限定的な機能のみ利用可能', 
JSON_OBJECT(
    'text_correction', true,
    'view_own_history', false,
    'word_list_management', false,
    'download_results', true,
    'profile_edit', false
), 1);

-- プレミアムユーザーロール
INSERT IGNORE INTO role (id, name, display_name, description, permissions, is_active) VALUES 
(4, 'PREMIUM', 'プレミアムユーザー', '高度な機能を利用できる', 
JSON_OBJECT(
    'text_correction', true,
    'word_list_management', true,
    'view_own_history', true,
    'download_results', true,
    'profile_edit', true,
    'advanced_models', true,
    'batch_processing', true,
    'api_access', true,
    'priority_support', true
), 1);

-- ============================================================================
-- 4. 既存ユーザーへのデフォルトロール割り当て
-- ============================================================================

-- 管理者ユーザーにADMINロールを割り当て
INSERT IGNORE INTO user_role (user_id, role_id, assigned_by, assigned_at) 
SELECT 1, 1, 1, NOW() 
WHERE EXISTS (SELECT 1 FROM user WHERE id = 1 AND username = 'admin');

-- テストユーザーにUSERロールを割り当て
INSERT IGNORE INTO user_role (user_id, role_id, assigned_by, assigned_at) 
SELECT 2, 2, 1, NOW() 
WHERE EXISTS (SELECT 1 FROM user WHERE id = 2 AND username = 'testuser');

-- デモユーザーにGUESTロールを割り当て
INSERT IGNORE INTO user_role (user_id, role_id, assigned_by, assigned_at) 
SELECT 3, 3, 1, NOW() 
WHERE EXISTS (SELECT 1 FROM user WHERE id = 3 AND username = 'demo');

-- ============================================================================
-- 5. ロール管理用ビューの作成
-- ============================================================================

-- ユーザーロール一覧ビュー
CREATE OR REPLACE VIEW user_role_view AS
SELECT 
    u.id as user_id,
    u.username,
    u.email,
    u.is_active as user_active,
    r.id as role_id,
    r.name as role_name,
    r.display_name as role_display_name,
    r.description as role_description,
    ur.assigned_at,
    ur.expires_at,
    ur.is_active as role_assignment_active,
    CASE 
        WHEN ur.expires_at IS NULL THEN '無期限'
        WHEN ur.expires_at > NOW() THEN '有効'
        ELSE '期限切れ'
    END as status
FROM user u
LEFT JOIN user_role ur ON u.id = ur.user_id AND ur.is_active = 1
LEFT JOIN role r ON ur.role_id = r.id AND r.is_active = 1
WHERE u.is_active = 1;

-- ロール権限一覧ビュー
CREATE OR REPLACE VIEW role_permissions_view AS
SELECT 
    r.id as role_id,
    r.name as role_name,
    r.display_name,
    r.description,
    r.permissions,
    COUNT(ur.user_id) as assigned_users_count,
    r.is_active,
    r.created_at,
    r.updated_at
FROM role r
LEFT JOIN user_role ur ON r.id = ur.role_id AND ur.is_active = 1
GROUP BY r.id, r.name, r.display_name, r.description, r.permissions, r.is_active, r.created_at, r.updated_at;

-- ============================================================================
-- 6. ロール管理用プロシージャ
-- ============================================================================

DELIMITER //

-- ユーザーにロールを割り当てるプロシージャ
CREATE PROCEDURE IF NOT EXISTS AssignRoleToUser(
    IN p_user_id BIGINT,
    IN p_role_id BIGINT,
    IN p_assigned_by BIGINT,
    IN p_expires_at DATETIME
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 既存の同じロール割り当てを無効化
    UPDATE user_role 
    SET is_active = 0 
    WHERE user_id = p_user_id AND role_id = p_role_id;
    
    -- 新しいロール割り当てを挿入
    INSERT INTO user_role (user_id, role_id, assigned_by, assigned_at, expires_at, is_active)
    VALUES (p_user_id, p_role_id, p_assigned_by, NOW(), p_expires_at, 1);
    
    COMMIT;
    
    SELECT 'ロールが正常に割り当てられました' as result;
END //

-- ユーザーからロールを削除するプロシージャ
CREATE PROCEDURE IF NOT EXISTS RemoveRoleFromUser(
    IN p_user_id BIGINT,
    IN p_role_id BIGINT
)
BEGIN
    UPDATE user_role 
    SET is_active = 0 
    WHERE user_id = p_user_id AND role_id = p_role_id;
    
    SELECT CONCAT('ユーザーID ', p_user_id, ' からロールID ', p_role_id, ' を削除しました') as result;
END //

-- 期限切れロール割り当てをクリーンアップするプロシージャ
CREATE PROCEDURE IF NOT EXISTS CleanupExpiredRoles()
BEGIN
    DECLARE expired_count INT DEFAULT 0;
    
    UPDATE user_role 
    SET is_active = 0 
    WHERE expires_at IS NOT NULL 
    AND expires_at < NOW() 
    AND is_active = 1;
    
    SET expired_count = ROW_COUNT();
    
    SELECT CONCAT('期限切れロール割り当て数: ', expired_count) as result;
END //

DELIMITER ;

-- ============================================================================
-- 7. ロール管理用便利クエリ
-- ============================================================================

-- /* ユーザー別ロール一覧 */
-- SELECT 
--     u.username,
--     GROUP_CONCAT(r.display_name ORDER BY r.name) as roles,
--     GROUP_CONCAT(
--         CASE 
--             WHEN ur.expires_at IS NULL THEN '無期限'
--             WHEN ur.expires_at > NOW() THEN CONCAT('有効期限: ', DATE_FORMAT(ur.expires_at, '%Y-%m-%d'))
--             ELSE '期限切れ'
--         END 
--         ORDER BY r.name
--     ) as role_status
-- FROM user u
-- LEFT JOIN user_role ur ON u.id = ur.user_id AND ur.is_active = 1
-- LEFT JOIN role r ON ur.role_id = r.id AND r.is_active = 1
-- WHERE u.is_active = 1
-- GROUP BY u.id, u.username
-- ORDER BY u.username;

-- /* ロール別ユーザー数 */
-- SELECT 
--     r.name as role_name,
--     r.display_name,
--     COUNT(ur.user_id) as user_count,
--     COUNT(CASE WHEN ur.expires_at IS NULL OR ur.expires_at > NOW() THEN 1 END) as active_assignments
-- FROM role r
-- LEFT JOIN user_role ur ON r.id = ur.role_id AND ur.is_active = 1
-- WHERE r.is_active = 1
-- GROUP BY r.id, r.name, r.display_name
-- ORDER BY user_count DESC;
