# サーバーポート設定
server.port=8080

# OpenAI API設定
openai.api.key=YOUR_API_KEY_HERE
openai.api.url=https://api.openai.com/v1/chat/completions

# アプリケーション設定
app.config.directory=${user.home}/.teams_transcript_cleaner
app.config.file=${app.config.directory}/config.json
app.word-history.file=${app.config.directory}/word_history.json

# ファイルアップロード設定
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# ロギング設定
logging.level.com.transcriptcleaner=INFO
logging.file.name=${app.config.directory}/application.log

# CORS設定
spring.web.cors.allowed-origins=http://localhost:3000
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true

# MySQL接続設定
spring.datasource.url=*****************************************************************************
spring.datasource.username=root
spring.datasource.password=123
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect