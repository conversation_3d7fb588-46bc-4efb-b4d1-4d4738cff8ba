# システムアーキテクチャ

- GUIアプリケーションを提供する。
- クリーニング処理は、/backend にJava Spring Bootで実装する。
- GUIは、`/frontend` にReactで実装する。

## 構造設計

- モジュール分割：
  1. `frontend/`：画面入出力層
     - `components/Sidebar.js`：
       - ファイルアップロードハンドラ：議事録テキストファイル（TXT）と誤字脱字一覧（CSV）のアップロードを処理する。
       - モデル選択 UI：OpenAIモデルを選択するためのUIを提供する。
       - プロンプトカスタマイザ：OpenAI APIへの指示をカスタマイズするためのUIを提供する。
       - CSV表示：アップロードされたCSVをExcelライクなグリッドで表示し、編集可能。
     - `components/MainContent.js`：
       - 訂正前後のテキストを表示し、差分表示機能を提供する。
       - 差分表示：段落レベルで比較し、差分がある場合は単語レベルでハイライト。
       - ボタン：訂正後テキストのダウンロードやコピー機能を提供する。
  2. `backend/`：業務処理層
     - `service/OpenAiService.java`：OpenAI APIとの通信を処理し、テキストの訂正を行う。
     - `service/CsvService.java`：CSVファイルを解析し、誤字脱字リストを管理する。
     - `service/ConfigService.java`：設定ファイルの読み込みと管理を行う。
     - `controller/ApiController.java`：REST APIエンドポイントを提供し、フロントエンドと連携する。

---

## 機能概要

- **フロントエンド**：
  - ファイルアップロード（TXT, CSV）
  - OpenAIモデル選択とプロンプト入力
  - 訂正前後のテキスト表示
  - 差分表示（段落・単語レベル）
  - 訂正後テキストのダウンロード

- **バックエンド**：
  - OpenAI API連携
  - CSV解析と管理
  - REST APIエンドポイント
