import React from 'react';
import { Box, Typography, TextField, Button, Checkbox, FormControlLabel } from '@mui/material';
import { diffWords } from 'diff';

// Constants for styling
const STYLE_CONSTANTS = {
  ADDED_BG: '#e6ffed',
  REMOVED_BG: '#ffeef0',
  ADDED_COLOR: '#22863a',
  REMOVED_COLOR: '#cb2431',
  BORDER_RADIUS: 2
};

function TranscriptCleanerContent({
  originalText,
  correctedText,
  setCorrectedText,
  isProcessing,
  handleCorrectText,
  handleDownload,
  processingInfo,
  error,
  language
}) {
  const [showDiff, setShowDiff] = React.useState(false);
  const [tokenizer, setTokenizer] = React.useState(null);
  const [tokenizerLoading, setTokenizerLoading] = React.useState(false);

  // kuromoji.jsの初期化（初回のみ）
  React.useEffect(() => {
    if (!tokenizer && window.kuromoji) {
      setTokenizerLoading(true);
      window.kuromoji.builder({ dicPath: '/dict/' }).build((err, _tokenizer) => {
        setTokenizer(_tokenizer);
        setTokenizerLoading(false);
      });
    }
  }, [tokenizer]);

  // Label definitions
  const labels = {
    ja: {
      original: '元の議事録',
      corrected: '訂正後の議事録',
      showDiff: '差分を表示',
      detailedDiff: '詳細な差分',
      paragraph: '段落',
      wordLevel: '単語レベルの差分:',
      legend: '凡例:',
      added: '+ 追加',
      removed: '- 削除',
      copy: '元テキストをコピー',
      download: '最終結果をダウンロード'
    },
    en: {
      original: 'Original Transcript',
      corrected: 'Corrected Transcript',
      showDiff: 'Show Differences',
      detailedDiff: 'Detailed Differences',
      paragraph: 'Paragraph',
      wordLevel: 'Word-Level Differences:',
      legend: 'Legend:',
      added: '+ Added',
      removed: '- Removed',
      copy: 'Copy Original Text',
      download: 'Download Final Result'
    }
  };
  const t = labels[language] || labels['ja'];

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', paddingLeft: '20px' }}>
      {/* Top Section: Original and Corrected Transcript Areas */}
      <Box sx={{ display: 'flex', gap: 2, flex: '0 0 50%', overflow: 'hidden' }}>
        <Box sx={{ flex: 1, overflow: 'auto' }}>
          <TextField
            label={t.original}
            value={originalText}
            multiline
            fullWidth
            InputProps={{ readOnly: true }}
          />
        </Box>
        <Box sx={{ flex: 1, overflow: 'auto' }}>
          <TextField
            label={t.corrected}
            value={correctedText}
            onChange={(e) => setCorrectedText(e.target.value)}
            multiline
            fullWidth
          />
        </Box>
      </Box>

      {/* Bottom Section: Diff Display and Buttons */}
      <Box sx={{ flex: '1 1 auto', mt: 2 }}>
        <FormControlLabel
          control={
            <Checkbox
              checked={showDiff}
              onChange={(e) => setShowDiff(e.target.checked)}
              disabled={!originalText || !correctedText}
            />
          }
          label={t.showDiff}
        />

        {showDiff && (
          <Box sx={{ mt: 2 }}>
            {/* Legend */}
            <Box sx={{ mb: 2, display: 'flex', gap: 2, alignItems: 'center' }}>
              <Typography variant="subtitle2">{t.legend}</Typography>
              <Box component="span" sx={{ background: STYLE_CONSTANTS.ADDED_BG, px: 1, borderRadius: 1, mx: 1 }}>{t.added}</Box>
              <Box component="span" sx={{ background: STYLE_CONSTANTS.REMOVED_BG, px: 1, borderRadius: 1, mx: 1 }}>{t.removed}</Box>
            </Box>
            {/* Paragraph-level word diff highlight */}
            {language === 'ja' && tokenizerLoading && (
              <Typography color="text.secondary">形態素解析エンジン（kuromoji.js）を初期化中...</Typography>
            )}
            <Box sx={{ p: 2, background: '#fafbfc', borderRadius: STYLE_CONSTANTS.BORDER_RADIUS, fontFamily: 'monospace', whiteSpace: 'pre-wrap' }}>
              {(() => {
                // 段落ごとに分割
                const originalParagraphs = originalText.split(/\r?\n/);
                const correctedParagraphs = correctedText.split(/\r?\n/);
                const maxLen = Math.max(originalParagraphs.length, correctedParagraphs.length);
                const isJapanese = language === 'ja';
                const rows = [];
                for (let i = 0; i < maxLen; i++) {
                  let orig = originalParagraphs[i] || '';
                  let corr = correctedParagraphs[i] || '';
                  // 日本語の場合はkuromojiで分かち書き
                  if (isJapanese && tokenizer) {
                    const origTokens = tokenizer.tokenize(orig).map(token => token.surface_form);
                    const corrTokens = tokenizer.tokenize(corr).map(token => token.surface_form);
                    console.log('origTokens:', origTokens);
                    console.log('corrTokens:', corrTokens);
                    orig = origTokens.join(' ');
                    corr = corrTokens.join(' ');
                  }
                  // 段落ごとに単語レベルdiff
                  const diff = diffWords(orig, corr);
                  rows.push(
                    <Box key={i} sx={{ mb: 2 }}>
                      <Typography variant="caption" color="text.secondary">{t.paragraph} {i + 1}</Typography>
                      <Box>
                        {/* 変更前段落（打消し線付き） */}
                        {diff.map((part, idx) =>
                          part.removed ? (
                            <span key={idx} style={{ background: STYLE_CONSTANTS.REMOVED_BG, color: STYLE_CONSTANTS.REMOVED_COLOR, textDecoration: 'line-through' }}>{part.value}</span>
                          ) : part.added ? null : (
                            <span key={idx}>{part.value}</span>
                          )
                        )}
                      </Box>
                      <Box>
                        {/* 変更後段落（追加分ハイライト） */}
                        {diff.map((part, idx) =>
                          part.added ? (
                            <span key={idx} style={{ background: STYLE_CONSTANTS.ADDED_BG, color: STYLE_CONSTANTS.ADDED_COLOR, fontWeight: 700 }}>{part.value}</span>
                          ) : part.removed ? null : (
                            <span key={idx}>{part.value}</span>
                          )
                        )}
                      </Box>
                    </Box>
                  );
                }
                return rows;
              })()}
            </Box>
          </Box>
        )}

        {/* Buttons */}
        <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
          <Button
            variant="outlined"
            onClick={() => setCorrectedText(originalText)}
            disabled={!originalText}
          >
            {t.copy}
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={handleDownload}
            disabled={!correctedText}
          >
            {t.download}
          </Button>
        </Box>

        {error && <Typography color="error" sx={{ mt: 2 }}>{error}</Typography>}
      </Box>
    </Box>
  );
}

export default TranscriptCleanerContent;
