-- TranscriptCleaner Database Maintenance Scripts
-- データベースメンテナンス用SQLファイル

-- ============================================================================
-- 1. データベース状態確認クエリ
-- ============================================================================

-- テーブル一覧とレコード数確認
SELECT 
    TABLE_NAME as 'テーブル名',
    TABLE_ROWS as 'レコード数',
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as 'サイズ(MB)',
    TABLE_COMMENT as 'コメント'
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'transcriptcleaner'
ORDER BY TABLE_ROWS DESC;

-- ユーザー統計情報
SELECT 
    COUNT(*) as '総ユーザー数',
    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as 'アクティブユーザー数',
    SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as '非アクティブユーザー数',
    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as '30日以内の新規ユーザー'
FROM user;

-- 訂正履歴統計
SELECT 
    COUNT(*) as '総訂正回数',
    COUNT(DISTINCT user_id) as '利用ユーザー数',
    SUM(COALESCE(cost, 0)) as '総コスト(USD)',
    AVG(COALESCE(token_count, 0)) as '平均トークン数',
    mode as '処理モード',
    model as 'AIモデル',
    COUNT(*) as '使用回数'
FROM correction_history
GROUP BY mode, model
ORDER BY COUNT(*) DESC;

-- ============================================================================
-- 2. パフォーマンス最適化クエリ
-- ============================================================================

-- インデックス使用状況確認
SELECT 
    TABLE_NAME as 'テーブル名',
    INDEX_NAME as 'インデックス名',
    COLUMN_NAME as 'カラム名',
    CARDINALITY as 'カーディナリティ'
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'transcriptcleaner'
ORDER BY TABLE_NAME, INDEX_NAME;

-- 重複データ確認
-- ユーザー名の重複チェック
SELECT username, COUNT(*) as count
FROM user 
GROUP BY username 
HAVING COUNT(*) > 1;

-- メールアドレスの重複チェック
SELECT email, COUNT(*) as count
FROM user 
GROUP BY email 
HAVING COUNT(*) > 1;

-- ============================================================================
-- 3. データクリーンアップクエリ
-- ============================================================================

-- 期限切れセッションの削除
DELETE FROM session 
WHERE expires_at < NOW();

-- 非アクティブユーザーの古いデータ削除（90日以上前）
-- 注意: 実行前に必ずバックアップを取ること
-- DELETE ch FROM correction_history ch
-- JOIN user u ON ch.user_id = u.id
-- WHERE u.is_active = 0 
-- AND ch.created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 古い訂正履歴の削除（1年以上前、コスト情報のみ保持）
-- UPDATE correction_history 
-- SET original_text = '[削除済み]', corrected_text = '[削除済み]'
-- WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);

-- ============================================================================
-- 4. バックアップ用クエリ
-- ============================================================================

-- ユーザーデータのエクスポート
SELECT 
    id, username, email, created_at, updated_at, is_active
FROM user
INTO OUTFILE '/tmp/user_backup.csv'
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n';

-- 訂正履歴のエクスポート（個人情報除く）
SELECT 
    id, user_id, mode, model, cost, token_count, created_at
FROM correction_history
INTO OUTFILE '/tmp/correction_history_backup.csv'
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n';

-- ============================================================================
-- 5. セキュリティ関連クエリ
-- ============================================================================

-- 最近のログイン失敗を想定したセッション確認
SELECT 
    s.id as session_id,
    u.username,
    s.created_at as session_created,
    s.expires_at as session_expires,
    CASE 
        WHEN s.expires_at < NOW() THEN '期限切れ'
        ELSE '有効'
    END as status
FROM session s
JOIN user u ON s.user_id = u.id
ORDER BY s.created_at DESC
LIMIT 20;

-- パスワードハッシュの形式確認
SELECT 
    username,
    CASE 
        WHEN password_hash LIKE '$2a$%' THEN 'BCrypt'
        WHEN password_hash LIKE '$2b$%' THEN 'BCrypt'
        WHEN password_hash LIKE '$2y$%' THEN 'BCrypt'
        ELSE '不明な形式'
    END as hash_type,
    LENGTH(password_hash) as hash_length
FROM user
WHERE is_active = 1;

-- ============================================================================
-- 6. 監査・レポート用クエリ
-- ============================================================================

-- 月別利用統計
SELECT 
    DATE_FORMAT(created_at, '%Y-%m') as month,
    COUNT(*) as correction_count,
    COUNT(DISTINCT user_id) as unique_users,
    SUM(COALESCE(cost, 0)) as total_cost,
    AVG(COALESCE(token_count, 0)) as avg_tokens
FROM correction_history
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
GROUP BY DATE_FORMAT(created_at, '%Y-%m')
ORDER BY month DESC;

-- ユーザー別利用状況（上位10名）
SELECT 
    u.username,
    COUNT(ch.id) as correction_count,
    SUM(COALESCE(ch.cost, 0)) as total_cost,
    MAX(ch.created_at) as last_used,
    COUNT(wlh.id) as word_lists_count
FROM user u
LEFT JOIN correction_history ch ON u.id = ch.user_id
LEFT JOIN word_list_history wlh ON u.id = wlh.user_id
WHERE u.is_active = 1
GROUP BY u.id, u.username
ORDER BY correction_count DESC
LIMIT 10;

-- モデル別パフォーマンス
SELECT 
    model,
    COUNT(*) as usage_count,
    AVG(COALESCE(cost, 0)) as avg_cost,
    AVG(COALESCE(token_count, 0)) as avg_tokens,
    SUM(COALESCE(cost, 0)) as total_cost
FROM correction_history
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY model
ORDER BY usage_count DESC;

-- ============================================================================
-- 7. トラブルシューティング用クエリ
-- ============================================================================

-- 外部キー制約確認
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = 'transcriptcleaner'
AND REFERENCED_TABLE_NAME IS NOT NULL;

-- テーブルサイズとフラグメンテーション確認
SELECT 
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) as 'Size(MB)',
    ROUND((DATA_FREE / 1024 / 1024), 2) as 'Free(MB)',
    ROUND((DATA_FREE / (DATA_LENGTH + INDEX_LENGTH)) * 100, 2) as 'Fragmentation(%)'
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = 'transcriptcleaner'
AND (DATA_LENGTH + INDEX_LENGTH) > 0
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- ============================================================================
-- 8. 定期メンテナンス用プロシージャ
-- ============================================================================

DELIMITER //

-- 期限切れセッション削除プロシージャ
CREATE PROCEDURE IF NOT EXISTS CleanupExpiredSessions()
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    DELETE FROM session WHERE expires_at < NOW();
    SET deleted_count = ROW_COUNT();
    
    SELECT CONCAT('削除されたセッション数: ', deleted_count) as result;
END //

-- 統計情報更新プロシージャ
CREATE PROCEDURE IF NOT EXISTS UpdateStatistics()
BEGIN
    ANALYZE TABLE user, correction_history, session, word_list_history;
    SELECT '統計情報が更新されました' as result;
END //

DELIMITER ;
