package com.transcriptcleaner.service;

import com.transcriptcleaner.model.Role;
import com.transcriptcleaner.model.User;
import com.transcriptcleaner.model.UserRole;
import com.transcriptcleaner.repository.RoleRepository;
import com.transcriptcleaner.repository.UserRepository;
import com.transcriptcleaner.repository.UserRoleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Service
@RequiredArgsConstructor
@Slf4j
public class RoleService {
    
    private final RoleRepository roleRepository;
    private final UserRepository userRepository;
    private final UserRoleRepository userRoleRepository;

    /**
     * 全てのアクティブなロールを取得
     */
    public List<Role> getAllActiveRoles() {
        return roleRepository.findByIsActiveTrue();
    }

    /**
     * ロールIDでロールを取得
     */
    public Optional<Role> getRoleById(Long id) {
        return roleRepository.findById(id);
    }

    /**
     * ロール名でロールを取得
     */
    public Optional<Role> getRoleByName(String name) {
        return roleRepository.findByNameAndIsActiveTrue(name);
    }

    /**
     * 新しいロールを作成
     */
    @Transactional
    public Role createRole(String name, String displayName, String description, String permissions) {
        // ロール名の重複チェック
        if (roleRepository.existsByNameIgnoreCase(name)) {
            throw new IllegalArgumentException("ロール名 '" + name + "' は既に存在します");
        }

        Role role = Role.builder()
                .name(name.toUpperCase())
                .displayName(displayName)
                .description(description)
                .permissions(permissions)
                .isActive(true)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        Role savedRole = roleRepository.save(role);
        log.info("新しいロールが作成されました: {}", savedRole);
        return savedRole;
    }

    /**
     * ロールを更新
     */
    @Transactional
    public Role updateRole(Long roleId, String displayName, String description, String permissions) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException("ロールが見つかりません: " + roleId));

        // システムロールの場合は権限のみ更新可能
        if (role.isSystemRole()) {
            role.setPermissions(permissions);
        } else {
            role.setDisplayName(displayName);
            role.setDescription(description);
            role.setPermissions(permissions);
        }

        Role updatedRole = roleRepository.save(role);
        log.info("ロールが更新されました: {}", updatedRole);
        return updatedRole;
    }

    /**
     * ロールを無効化（削除）
     */
    @Transactional
    public void deactivateRole(Long roleId) {
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException("ロールが見つかりません: " + roleId));

        if (role.isSystemRole()) {
            throw new IllegalArgumentException("システムロールは削除できません: " + role.getName());
        }

        role.setActive(false);
        roleRepository.save(role);

        // 関連するユーザーロール割り当ても無効化
        userRoleRepository.deactivateUserRole(null, roleId);
        
        log.info("ロールが無効化されました: {}", role);
    }

    /**
     * ユーザーにロールを割り当て
     */
    @Transactional
    public UserRole assignRoleToUser(Long userId, Long roleId, Long assignedByUserId, LocalDateTime expiresAt) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("ユーザーが見つかりません: " + userId));
        
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException("ロールが見つかりません: " + roleId));
        
        User assignedBy = userRepository.findById(assignedByUserId)
                .orElseThrow(() -> new IllegalArgumentException("割り当て者が見つかりません: " + assignedByUserId));

        // 既存の同じロール割り当てがあるかチェック
        Optional<UserRole> existingUserRole = userRoleRepository.findByUserAndRoleAndIsActiveTrue(user, role);
        if (existingUserRole.isPresent()) {
            throw new IllegalArgumentException("ユーザーは既にこのロールを持っています");
        }

        UserRole userRole = UserRole.builder()
                .user(user)
                .role(role)
                .assignedBy(assignedBy)
                .assignedAt(LocalDateTime.now())
                .expiresAt(expiresAt)
                .isActive(true)
                .build();

        UserRole savedUserRole = userRoleRepository.save(userRole);
        log.info("ユーザーにロールが割り当てられました: ユーザー={}, ロール={}, 割り当て者={}", 
                user.getUsername(), role.getName(), assignedBy.getUsername());
        
        return savedUserRole;
    }

    /**
     * ユーザーからロールを削除
     */
    @Transactional
    public void removeRoleFromUser(Long userId, Long roleId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new IllegalArgumentException("ユーザーが見つかりません: " + userId));
        
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new IllegalArgumentException("ロールが見つかりません: " + roleId));

        int deactivatedCount = userRoleRepository.deactivateUserRole(userId, roleId);
        
        if (deactivatedCount > 0) {
            log.info("ユーザーからロールが削除されました: ユーザー={}, ロール={}", 
                    user.getUsername(), role.getName());
        } else {
            throw new IllegalArgumentException("指定されたロール割り当てが見つかりません");
        }
    }

    /**
     * ユーザーのアクティブなロールを取得
     */
    public List<UserRole> getUserActiveRoles(Long userId) {
        return userRoleRepository.findActiveRolesByUserId(userId);
    }

    /**
     * ユーザーが特定のロールを持っているかチェック
     */
    public boolean hasUserRole(Long userId, String roleName) {
        return userRoleRepository.hasUserRole(userId, roleName);
    }

    /**
     * ユーザーが特定の権限を持っているかチェック
     */
    public boolean hasUserPermission(Long userId, String permission) {
        List<UserRole> userRoles = userRoleRepository.findActiveRolesByUserId(userId);
        return userRoles.stream()
                .map(UserRole::getRole)
                .anyMatch(role -> role.hasPermission(permission));
    }

    /**
     * 期限切れのロール割り当てをクリーンアップ
     */
    @Transactional
    public int cleanupExpiredRoles() {
        int deactivatedCount = userRoleRepository.deactivateExpiredRoles();
        log.info("期限切れロール割り当てをクリーンアップしました: {} 件", deactivatedCount);
        return deactivatedCount;
    }

    /**
     * 指定日数以内に期限切れになるロール割り当てを取得
     */
    public List<UserRole> getRolesExpiringWithinDays(int days) {
        LocalDateTime expiryDate = LocalDateTime.now().plusDays(days);
        return userRoleRepository.findRolesExpiringBefore(expiryDate);
    }

    /**
     * ロール統計情報を取得
     */
    public List<Object[]> getRoleStatistics() {
        return userRoleRepository.getRoleUserStatistics();
    }

    /**
     * ユーザー統計情報を取得
     */
    public List<Object[]> getUserStatistics() {
        return userRoleRepository.getUserRoleStatistics();
    }

    /**
     * デフォルトロールをユーザーに割り当て
     */
    @Transactional
    public void assignDefaultRole(User user) {
        Optional<Role> defaultRole = getRoleByName(Role.USER);
        if (defaultRole.isPresent()) {
            try {
                assignRoleToUser(user.getId(), defaultRole.get().getId(), 1L, null); // システムによる割り当て
            } catch (IllegalArgumentException e) {
                // 既にロールを持っている場合は無視
                log.debug("ユーザー {} は既にデフォルトロールを持っています", user.getUsername());
            }
        }
    }
}
