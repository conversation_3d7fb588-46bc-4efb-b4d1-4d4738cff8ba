import React, { useState } from "react";
import {
  Box,
  Button,
  Typography,
  TextField,
  Select,
  MenuItem,
  CircularProgress,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Collapse,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import {
  ExpandLess,
  ExpandMore,
  Description,
  Settings,
  ExitToApp,
} from "@mui/icons-material";

function Sidebar({
  mode,
  setMode,
  model,
  setModel,
  customPrompt,
  setCustomPrompt,
  wordList,
  setWordList,
  handleCorrectText,
  originalText,
  setOriginalText,
  isProcessing,
  language,
  setLanguage,
  onLogout,
  selectedMenu,
  setSelectedMenu,
  userRole,
  adminSubMenu,
  setAdminSubMenu,
}) {
  const [csvData, setCsvData] = useState([]);
  const [openDrawers, setOpenDrawers] = useState({
    transcript: true,
    admin: false,
  });

  // メニュー定義
  const menuItems = [
    {
      id: 'transcript',
      label: 'トランスクリプト訂正',
      icon: Description,
      roles: ['USER', 'ADMIN', 'PREMIUM', 'GUEST'],
    },
    {
      id: 'admin',
      label: '管理機能',
      icon: Settings,
      roles: ['ADMIN'],
    },
  ];

  // ドロワーの開閉制御
  const handleDrawerToggle = (menuId) => {
    setOpenDrawers(prev => ({
      ...prev,
      [menuId]: !prev[menuId]
    }));

    // メニュー選択時は該当メニューを開く
    if (!openDrawers[menuId]) {
      setSelectedMenu(menuId);
    }
  };

  const handleCsvUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        const text = reader.result;
        const rows = text.split("\n").map((row, rowIndex) => {
          const cells = row.split(",");
          return {
            id: rowIndex,
            ...Object.fromEntries(
              cells.map((cell, colIndex) => [`col${colIndex}`, cell])
            ),
          };
        });
        setCsvData(rows);

        // CSVデータをwordList形式に変換
        const wordListText = text.split("\n")
          .filter(row => row.trim())
          .map(row => row.trim())
          .join("\n");
        setWordList(wordListText);
      };
      reader.readAsText(file);
    }
  };

  const handleCellEdit = (params) => {
    const updatedCsvData = csvData.map((row) => {
      if (row.id === params.id) {
        return { ...row, [params.field]: params.value };
      }
      return row;
    });
    setCsvData(updatedCsvData);
  };

  const columns =
    csvData.length > 0
      ? Object.keys(csvData[0])
          .filter((key) => key !== "id")
          .map((key) => ({
            field: key,
            headerName: key.replace("col", "Column "),
            editable: true,
            flex: 1,
          }))
      : [];

  // ラベル定義
  const labels = {
    ja: {
      sidebar: "トランスクリプトクリーナ",
      selectLanguage: "言語選択",
      selectModel: "モデル選択",
      customPrompt: "カスタムプロンプト",
      uploadTranscript: "議事録アップロード",
      uploadTxt: "TXTアップロード",
      uploadWordList: "誤字脱字リストアップロード",
      uploadCsv: "CSVアップロード",
      execute: "訂正実行",
    },
    en: {
      sidebar: "TranscriptCleaner",
      selectLanguage: "Select Language",
      selectModel: "Select Model",
      customPrompt: "Custom Prompt",
      uploadTranscript: "Upload Transcript",
      uploadTxt: "Upload TXT",
      uploadWordList: "Upload Word List",
      uploadCsv: "Upload CSV",
      execute: "Execute Correction",
    },
  };
  const t = labels[language] || labels["ja"];

  // 権限チェック関数
  const hasPermission = (requiredRoles) => {
    return requiredRoles.includes(userRole);
  };

  // トランスクリプト訂正の設定コンテンツ
  const renderTranscriptSettings = () => (
    <Box sx={{ px: 2, pb: 2 }}>
      {/* Language Selection */}
      <Typography variant="subtitle2" sx={{ mb: 0.5, color: "#888" }}>
        {t.selectLanguage}
      </Typography>
      <Select
        value={language}
        onChange={(e) => setLanguage(e.target.value)}
        fullWidth
        size="small"
        sx={{ mb: 2, background: "#f5f6fa", borderRadius: 1 }}
      >
        <MenuItem value="ja">日本語</MenuItem>
        <MenuItem value="en">English</MenuItem>
      </Select>

      {/* OpenAI Model Selection */}
      <Typography variant="subtitle2" sx={{ mb: 0.5, color: "#888" }}>
        {t.selectModel}
      </Typography>
      <Select
        value={model}
        onChange={(e) => setModel(e.target.value)}
        fullWidth
        size="small"
        sx={{ mb: 2, background: "#f5f6fa", borderRadius: 1 }}
      >
        <MenuItem value="gpt-4">GPT-4</MenuItem>
        <MenuItem value="gpt-4-turbo">GPT-4 Turbo</MenuItem>
        <MenuItem value="gpt-3.5-turbo">GPT-3.5 Turbo</MenuItem>
      </Select>

      {/* Mode Selection */}
      <Typography variant="subtitle2" sx={{ mb: 0.5, color: "#888" }}>
        処理モード選択
      </Typography>
      <Select
        value={mode}
        onChange={e => setMode(e.target.value)}
        fullWidth
        size="small"
        sx={{ mb: 2, background: "#f5f6fa", borderRadius: 1 }}
      >
        <MenuItem value="proofreading">誤字脱字修正</MenuItem>
        <MenuItem value="grammar">文法訂正</MenuItem>
        <MenuItem value="summary">要約</MenuItem>
      </Select>

      {/* Custom Prompt Input */}
      <Typography variant="subtitle2" sx={{ mt: 2, mb: 0.5, color: "#888" }}>
        {t.customPrompt}
      </Typography>
      <TextField
        value={customPrompt}
        onChange={(e) => setCustomPrompt(e.target.value)}
        placeholder={
          language === "ja" ? "追加指示を入力" : "Enter custom instructions"
        }
        fullWidth
        multiline
        minRows={2}
        sx={{ background: "#f5f6fa", borderRadius: 1 }}
      />

      {/* File Upload Buttons */}
      <Typography variant="subtitle2" sx={{ mt: 2, mb: 0.5, color: "#888" }}>
        {t.uploadTranscript}
      </Typography>
      <Button
        variant="contained"
        component="label"
        fullWidth
        sx={{
          mb: 1,
          borderRadius: 2,
          boxShadow: "none",
          textTransform: "none",
        }}
      >
        {t.uploadTxt}
        <input
          type="file"
          accept=".txt"
          hidden
          onChange={(e) => {
            const file = e.target.files[0];
            if (file) {
              const reader = new FileReader();
              reader.onload = () => setOriginalText(reader.result);
              reader.readAsText(file);
            }
          }}
        />
      </Button>

      <Typography variant="subtitle2" sx={{ mt: 2, mb: 0.5, color: "#888" }}>
        {t.uploadWordList}
      </Typography>
      <Button
        variant="contained"
        component="label"
        fullWidth
        sx={{
          mb: 1,
          borderRadius: 2,
          boxShadow: "none",
          textTransform: "none",
        }}
      >
        {t.uploadCsv}
        <input type="file" accept=".csv" hidden onChange={handleCsvUpload} />
      </Button>

      {/* Display CSV Data in Excel-like Grid */}
      {csvData.length > 0 && (
        <Box sx={{ height: 200, mt: 2 }}>
          <DataGrid
            rows={csvData}
            columns={columns}
            onCellEditCommit={handleCellEdit}
            disableSelectionOnClick
            sx={{ background: "#f5f6fa", borderRadius: 1 }}
            hideFooter={true}
          />
        </Box>
      )}

      {/* Correction Execution Button */}
      <Button
        variant="contained"
        color="primary"
        fullWidth
        sx={{
          mt: 2,
          borderRadius: 2,
          fontWeight: 700,
          boxShadow: "none",
          textTransform: "none",
          py: 1,
        }}
        onClick={() =>
          handleCorrectText(originalText, mode, model, customPrompt, wordList)
        }
        disabled={isProcessing || !originalText || (mode === 'proofreading' && !wordList)}
      >
        {isProcessing ? (
          <>
            <CircularProgress size={22} sx={{ color: "white", mr: 1 }} />
            {language === "ja" ? "処理中..." : "Processing..."}
          </>
        ) : !originalText ? (
          language === "ja" ? "テキストをアップロード" : "Upload Text"
        ) : mode === 'proofreading' && !wordList ? (
          language === "ja" ? "CSVをアップロード" : "Upload CSV"
        ) : (
          t.execute
        )}
      </Button>
    </Box>
  );

  // 管理機能のサブメニュー処理
  const handleAdminSubMenu = (subMenu) => {
    setAdminSubMenu(subMenu);
    // 管理機能が選択されていない場合は選択する
    if (selectedMenu !== 'admin') {
      setSelectedMenu('admin');
    }
  };

  // 管理機能の設定コンテンツ
  const renderAdminSettings = () => (
    <Box sx={{ px: 2, pb: 2 }}>
      <Typography variant="subtitle2" sx={{ mb: 2, color: "#888" }}>
        管理機能
      </Typography>

      <Button
        variant={adminSubMenu === 'users' ? "contained" : "outlined"}
        fullWidth
        sx={{ mb: 1, textTransform: "none" }}
        onClick={() => handleAdminSubMenu('users')}
      >
        👥 ユーザー管理
      </Button>

      <Button
        variant={adminSubMenu === 'roles' ? "contained" : "outlined"}
        fullWidth
        sx={{ mb: 1, textTransform: "none" }}
        onClick={() => handleAdminSubMenu('roles')}
      >
        🔐 ロール管理
      </Button>

      <Button
        variant={adminSubMenu === 'statistics' ? "contained" : "outlined"}
        fullWidth
        sx={{ mb: 1, textTransform: "none" }}
        onClick={() => handleAdminSubMenu('statistics')}
      >
        📊 統計情報
      </Button>
    </Box>
  );

  return (
    <Box
      sx={{
        background: "#fff",
        borderRadius: 3,
        boxShadow: "0 2px 12px rgba(0,0,0,0.08)",
        height: "100%",
        overflowY: "auto",
        display: "flex",
        flexDirection: "column",
      }}
    >
      {/* ヘッダー */}
      <Box sx={{ p: 3, borderBottom: "1px solid #e0e0e0" }}>
        <Typography
          variant="h6"
          sx={{ fontWeight: 700, letterSpacing: 1 }}
        >
          {t.sidebar}
        </Typography>
      </Box>

      {/* メニューリスト */}
      <List sx={{ flex: 1, py: 0 }}>
        {menuItems.map((item) => {
          if (!hasPermission(item.roles)) return null;

          const IconComponent = item.icon;
          const isOpen = openDrawers[item.id];
          const isSelected = selectedMenu === item.id;

          return (
            <Box key={item.id}>
              <ListItemButton
                onClick={() => handleDrawerToggle(item.id)}
                sx={{
                  py: 2,
                  px: 3,
                  backgroundColor: isSelected ? "#f5f6fa" : "transparent",
                  "&:hover": {
                    backgroundColor: "#f5f6fa",
                  },
                }}
              >
                <ListItemIcon sx={{ minWidth: 40 }}>
                  <IconComponent color={isSelected ? "primary" : "inherit"} />
                </ListItemIcon>
                <ListItemText
                  primary={item.label}
                  sx={{
                    "& .MuiListItemText-primary": {
                      fontWeight: isSelected ? 600 : 400,
                      color: isSelected ? "primary.main" : "inherit",
                    }
                  }}
                />
                {isOpen ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton>

              <Collapse in={isOpen} timeout="auto" unmountOnExit>
                {item.id === 'transcript' && renderTranscriptSettings()}
                {item.id === 'admin' && renderAdminSettings()}
              </Collapse>
            </Box>
          );
        })}
      </List>

      {/* フッター（ログアウトボタン） */}
      <Divider />
      <Box sx={{ p: 2 }}>
        {onLogout && (
          <Button
            variant="outlined"
            fullWidth
            onClick={onLogout}
            startIcon={<ExitToApp />}
            sx={{ textTransform: "none" }}
          >
            ログアウト
          </Button>
        )}
      </Box>
    </Box>
  );
}

export default Sidebar;
