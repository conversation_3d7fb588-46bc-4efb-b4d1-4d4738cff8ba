package com.transcriptcleaner.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.HashSet;

@Entity
@Table(name = "role")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Role {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true, length = 32)
    private String name;

    @Column(name = "display_name", nullable = false, length = 64)
    private String displayName;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column(columnDefinition = "JSON")
    private String permissions;

    @Column(name = "is_active", nullable = false, columnDefinition = "tinyint(1) default 1")
    private boolean isActive = true;

    @Column(name = "created_at", nullable = false, columnDefinition = "datetime default current_timestamp")
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false, columnDefinition = "datetime default current_timestamp on update current_timestamp")
    private LocalDateTime updatedAt;

    // ユーザーロール関連（双方向関係）
    @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private Set<UserRole> userRoles = new HashSet<>();

    // 権限チェック用のヘルパーメソッド
    public boolean hasPermission(String permission) {
        if (permissions == null || permissions.isEmpty()) {
            return false;
        }
        
        try {
            // JSON文字列から権限をチェック（簡易実装）
            return permissions.contains("\"" + permission + "\":true");
        } catch (Exception e) {
            return false;
        }
    }

    // 事前定義されたロール名の定数
    public static final String ADMIN = "ADMIN";
    public static final String USER = "USER";
    public static final String GUEST = "GUEST";
    public static final String PREMIUM = "PREMIUM";

    // 権限名の定数
    public static class Permissions {
        public static final String USER_MANAGEMENT = "user_management";
        public static final String ROLE_MANAGEMENT = "role_management";
        public static final String SYSTEM_SETTINGS = "system_settings";
        public static final String VIEW_ALL_DATA = "view_all_data";
        public static final String EXPORT_DATA = "export_data";
        public static final String DELETE_DATA = "delete_data";
        public static final String COST_MANAGEMENT = "cost_management";
        public static final String API_KEY_MANAGEMENT = "api_key_management";
        public static final String TEXT_CORRECTION = "text_correction";
        public static final String WORD_LIST_MANAGEMENT = "word_list_management";
        public static final String VIEW_OWN_HISTORY = "view_own_history";
        public static final String DOWNLOAD_RESULTS = "download_results";
        public static final String PROFILE_EDIT = "profile_edit";
        public static final String ADVANCED_MODELS = "advanced_models";
        public static final String BATCH_PROCESSING = "batch_processing";
        public static final String API_ACCESS = "api_access";
        public static final String PRIORITY_SUPPORT = "priority_support";
    }

    // JPA ライフサイクルコールバック
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (updatedAt == null) {
            updatedAt = LocalDateTime.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // ビジネスロジック用メソッド
    public boolean isSystemRole() {
        return ADMIN.equals(name) || USER.equals(name) || GUEST.equals(name);
    }

    public boolean canManageUsers() {
        return hasPermission(Permissions.USER_MANAGEMENT);
    }

    public boolean canManageRoles() {
        return hasPermission(Permissions.ROLE_MANAGEMENT);
    }

    public boolean canViewAllData() {
        return hasPermission(Permissions.VIEW_ALL_DATA);
    }

    public boolean canManageSystemSettings() {
        return hasPermission(Permissions.SYSTEM_SETTINGS);
    }

    // toString メソッドのオーバーライド
    @Override
    public String toString() {
        return "Role{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", displayName='" + displayName + '\'' +
                ", isActive=" + isActive +
                '}';
    }

    // equals と hashCode のオーバーライド
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Role)) return false;
        Role role = (Role) o;
        return name != null && name.equals(role.name);
    }

    @Override
    public int hashCode() {
        return name != null ? name.hashCode() : 0;
    }
}
