-- TranscriptCleaner Database Initial Data
-- 初期データ挿入用SQLファイル

-- ============================================================================
-- 初期ユーザーデータ
-- ============================================================================

-- 管理者ユーザー（username: admin, password: admin123）
-- BCryptハッシュ: $2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.
INSERT IGNORE INTO user (id, username, email, password_hash, is_active) VALUES 
(1, 'admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', 1);

-- テストユーザー（username: testuser, password: test123）
-- BCryptハッシュ: $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM7lbdxOmCrliKpfyR.m
INSERT IGNORE INTO user (id, username, email, password_hash, is_active) VALUES 
(2, 'testuser', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM7lbdxOmCrliKpfyR.m', 1);

-- デモユーザー（username: demo, password: demo123）
-- BCryptハッシュ: $2a$10$DowJonesIndex123456789012345678901234567890123456789012
INSERT IGNORE INTO user (id, username, email, password_hash, is_active) VALUES 
(3, 'demo', '<EMAIL>', '$2a$10$DowJonesIndex123456789012345678901234567890123456789012', 1);

-- ============================================================================
-- サンプル単語リスト
-- ============================================================================

-- 基本的な誤字脱字リスト
INSERT IGNORE INTO word_list_history (id, user_id, name, word_list, created_at, updated_at) VALUES 
(1, 1, '基本誤字脱字リスト', 
'誤字,正字
てすと,テスト
かいぎ,会議
ぎじろく,議事録
しりょう,資料
けんとう,検討
かくにん,確認
じっし,実施
ほうこく,報告
きょうゆう,共有', 
NOW(), NOW());

-- IT・技術用語リスト
INSERT IGNORE INTO word_list_history (id, user_id, name, word_list, created_at, updated_at) VALUES 
(2, 1, 'IT技術用語リスト',
'API,API
データベース,データベース
アプリケーション,アプリケーション
システム,システム
サーバー,サーバー
クライアント,クライアント
インターフェース,インターフェース
アルゴリズム,アルゴリズム
フレームワーク,フレームワーク
ライブラリ,ライブラリ',
NOW(), NOW());

-- ビジネス用語リスト
INSERT IGNORE INTO word_list_history (id, user_id, name, word_list, created_at, updated_at) VALUES 
(3, 2, 'ビジネス用語リスト',
'プロジェクト,プロジェクト
マネジメント,マネジメント
スケジュール,スケジュール
デッドライン,デッドライン
ミーティング,ミーティング
プレゼンテーション,プレゼンテーション
レポート,レポート
アジェンダ,アジェンダ
フィードバック,フィードバック
ステークホルダー,ステークホルダー',
NOW(), NOW());

-- ============================================================================
-- サンプル訂正履歴データ
-- ============================================================================

-- 管理者ユーザーのサンプル履歴
INSERT IGNORE INTO correction_history (id, user_id, original_text, corrected_text, mode, model, cost, token_count, created_at) VALUES 
(1, 1, 
'きょうのかいぎでは、あたらしいしすてむについてけんとうしました。',
'今日の会議では、新しいシステムについて検討しました。',
'proofreading', 'gpt-4', 0.002, 45, NOW() - INTERVAL 1 DAY);

INSERT IGNORE INTO correction_history (id, user_id, original_text, corrected_text, mode, model, cost, token_count, created_at) VALUES 
(2, 1,
'プロジェクトの進捗について報告します。現在、開発フェーズの80%が完了しており、予定通り来月末にはリリース予定です。',
'プロジェクトの進捗について報告いたします。現在、開発フェーズの80%が完了しており、予定通り来月末にリリース予定でございます。',
'grammar', 'gpt-4-turbo', 0.003, 67, NOW() - INTERVAL 2 HOURS);

-- テストユーザーのサンプル履歴
INSERT IGNORE INTO correction_history (id, user_id, original_text, corrected_text, mode, model, cost, token_count, created_at) VALUES 
(3, 2,
'今日のミーティングでは、新機能の仕様について議論しました。ユーザビリティの向上とパフォーマンスの最適化が主な課題として挙げられました。次回までに詳細な設計書を作成し、開発チームと共有する予定です。',
'本日の会議では新機能の仕様について協議し、ユーザビリティ向上とパフォーマンス最適化が主要課題として特定されました。次回会議までに詳細設計書を作成し、開発チームと情報共有を行う計画です。',
'summary', 'gpt-3.5-turbo', 0.001, 89, NOW() - INTERVAL 3 HOURS);

-- ============================================================================
-- 期限切れセッションのクリーンアップ用プロシージャ（オプション）
-- ============================================================================

DELIMITER //

CREATE PROCEDURE IF NOT EXISTS CleanupExpiredSessions()
BEGIN
    DELETE FROM session WHERE expires_at < NOW();
    SELECT ROW_COUNT() as deleted_sessions;
END //

DELIMITER ;

-- ============================================================================
-- 統計情報取得用ビュー（オプション）
-- ============================================================================

CREATE OR REPLACE VIEW user_statistics AS
SELECT 
    u.id,
    u.username,
    u.email,
    u.created_at as user_created_at,
    COUNT(ch.id) as total_corrections,
    COALESCE(SUM(ch.cost), 0) as total_cost,
    COALESCE(AVG(ch.token_count), 0) as avg_token_count,
    COUNT(wlh.id) as word_lists_count,
    MAX(ch.created_at) as last_correction_at
FROM user u
LEFT JOIN correction_history ch ON u.id = ch.user_id
LEFT JOIN word_list_history wlh ON u.id = wlh.user_id
WHERE u.is_active = 1
GROUP BY u.id, u.username, u.email, u.created_at;

-- ============================================================================
-- インデックス最適化（必要に応じて）
-- ============================================================================

-- 複合インデックスの追加
CREATE INDEX IF NOT EXISTS idx_correction_user_date ON correction_history(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_correction_mode_model ON correction_history(mode, model);
CREATE INDEX IF NOT EXISTS idx_wordlist_user_name ON word_list_history(user_id, name);

-- ============================================================================
-- 初期化完了メッセージ
-- ============================================================================

-- SELECT 'TranscriptCleaner database initialization completed successfully!' as message;
