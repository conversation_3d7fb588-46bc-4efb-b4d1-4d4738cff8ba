import axios from 'axios';

const API_BASE_URL = '/api';

// Add timeout configuration (60 seconds)
axios.defaults.timeout = 60000;

// Add request interceptor to include auth token
axios.interceptors.request.use(
  config => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Add response interceptor for better error handling
axios.interceptors.response.use(
  response => response,
  error => {
    // 認証エラーの場合はlocalStorageをクリア
    if (error.response?.status === 401) {
      localStorage.removeItem('authToken');
      localStorage.removeItem('authExpiry');
      // ページをリロードして認証状態をリセット
      window.location.reload();
    }

    // タイムアウトエラーの詳細表示
    if (error.code === 'ECONNABORTED') {
      console.error('API Timeout Error (60s exceeded):', error.message);
    } else {
      console.error('API Error:', error?.response?.data || error.message);
    }
    throw error;
  }
);

const ERROR_MESSAGES = {
  ja: {
    correction: 'テキスト訂正に失敗しました（タイムアウト: 60秒）',
    costReset: 'コスト履歴のリセットに失敗しました',
    wordListSave: '単語リストの保存に失敗しました'
  },
  en: {
    correction: 'Failed to correct text (timeout: 60s)',
    costReset: 'Failed to reset cost history',
    wordListSave: 'Failed to save word list'
  }
};

export const correctText = async (originalText, mode, model, customPrompt, wordList, language = 'ja') => {
  try {
    const response = await axios.post(`${API_BASE_URL}/correct-text`, {
      text: originalText,
      mode: mode,
      model: model,
      customPrompt: customPrompt,
      wordList: wordList,
    });
    return response.data;
  } catch (error) {
    console.error(ERROR_MESSAGES[language].correction, error);
    throw error;
  }
};

export const apiService = {
  // Reset cost history
  resetCost: async () => {
    try {
      const response = await axios.post(`${API_BASE_URL}/reset-cost`);
      return response.data;
    } catch (error) {
      console.error(ERROR_MESSAGES['ja'].costReset, error);
      throw error;
    }
  },

  // Save word list
  saveWordList: async (name, wordList) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/save-word-list`, { name, wordList });
      return response.data;
    } catch (error) {
      console.error(ERROR_MESSAGES['ja'].wordListSave, error);
      throw error;
    }
  },

  // Get word lists
  getWordLists: async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/word-lists`);
      return response.data;
    } catch (error) {
      console.error('Failed to get word lists:', error);
      throw error;
    }
  },
};

export const login = async (username, password) => {
  try {
    const response = await axios.post('/api/login', { username, password }, { withCredentials: true });
    return response.data;
  } catch (error) {
    throw error;
  }
};
