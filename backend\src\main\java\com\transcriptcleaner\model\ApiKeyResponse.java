package com.transcriptcleaner.model;

public class ApiKeyResponse {
    private boolean valid;
    private String message;
    private double totalCost;

    public ApiKeyResponse() {
    }

    public ApiKeyResponse(boolean valid, String message, double totalCost) {
        this.valid = valid;
        this.message = message;
        this.totalCost = totalCost;
    }

    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public double getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(double totalCost) {
        this.totalCost = totalCost;
    }

    public static ApiKeyResponseBuilder builder() {
        return new ApiKeyResponseBuilder();
    }

    public static class ApiKeyResponseBuilder {
        private boolean valid;
        private String message;
        private double totalCost;

        ApiKeyResponseBuilder() {
        }

        public ApiKeyResponseBuilder valid(boolean valid) {
            this.valid = valid;
            return this;
        }

        public ApiKeyResponseBuilder message(String message) {
            this.message = message;
            return this;
        }

        public ApiKeyResponseBuilder totalCost(double totalCost) {
            this.totalCost = totalCost;
            return this;
        }

        public ApiKeyResponse build() {
            return new ApiKeyResponse(valid, message, totalCost);
        }
    }
}