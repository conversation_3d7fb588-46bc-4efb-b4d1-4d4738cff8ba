# プロダクトの背景

Microsoft Teams の自動生成トランスクリプトは、会議の内容を記録するために非常に便利ですが、しばしば漏字・脱字や誤字が含まれます。

## 解決する問題

- Teamsのトランスクリプトの可読性の向上。
- トランスクリプトのクリーニング処理の効率化。
- ユーザーが自分のニーズに合わせてトランスクリプトをカスタマイズできるようにすること。

## UI 詳細要件

- 誤字脱字修正モード
  ![](../image/image_proofreading.png)
- 文法訂正モード
  ![](../image/image_grammar_check.png)
- 要約モード
  ![](../image/image_summary.png)

---

- 画面左側に設定用サイドバーを配置。メイン画面は左右2分割され、訂正前後のテキストなどが表示されます。
- 左側サイドバー上部に OpenAI モデル選択（コンボボックス、gpt-4o, gpt-4-turbo, gpt-4, gpt-3.5-turboなどを選択可能）
- 左側サイドバー上部にカスタムプロンプト入力欄（処理モードに応じた指示を入力）
- 左側サイドバーに訂正前の議事録（TXT 限定）アップロードボタン
- 左側サイドバーに誤字脱字一覧表示テキストエリア（CSV 形式表示・編集可、空の場合は「誤,正」をプレースホルダとして表示）
- 左側サイドバーに誤字脱字一覧（CSV 限定）アップロードボタン
- 左側サイドバー下部に訂正実行ボタン
- メイン画面の左列に訂正前の議事録（編集不可）、右列に訂正後の議事録（手修正可能なテキストエリア）を表示
- メイン画面のテキストエリア下部に差分表示のチェックボックスを配置し、チェックするとその下に詳細差分を表示
- メイン画面下部に、訂正後テキスト（右列）を訂正前テキスト（左列）にコピーするボタンを設置
- メイン画面下部に「最終修正結果をダウンロード」ボタン
- ユーザーが何度も OpenAI に再訂正を依頼できる運用を想定

---
