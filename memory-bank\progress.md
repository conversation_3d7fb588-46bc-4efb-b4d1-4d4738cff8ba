# 現在のステータス

- メモリバンクの初期化が進行中。
- `projectbrief.md`、`productContext.md`、`activeContext.md`、`systemPatterns.md`、`techContext.md`、`progress.md` ファイルが作成された。
- 各ファイルに初期コンテンツが記述された。

## 既知の問題

- なし

## 今後の計画

- GUIアプリケーション。
- クリーニング処理の実装。
- テストとデバッグ。
- `.clinerules` と `README.md` のレビュー

## 進捗状況

### 基本要件

- [x] 入力：トランスクリプト（テキスト）、訂正単語集（CSV）
- [x] 出力：訂正済み議事録
- [x] UI：ファイルアップロード、訂正実行、結果表示・ダウンロード
- [x] 訂正方法：OpenAI API ＋単語集
- [x] セットアップ・実行手順・API キー設定方法・サンプルデータ利用方法も含めること

### モジュール要件

- [x] UI 処理と業務ロジックの分離
- [x] モジュール配置：`ui_components/`と `processing/`の分離
- [x] インターフェース設計：明確なデータ構造定義
- [ ] テストコード実装：重要ロジックにはユニットテスト
