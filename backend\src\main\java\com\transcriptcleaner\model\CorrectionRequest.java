package com.transcriptcleaner.model;

public class CorrectionRequest {
    private String text;
    private String mode; // proofreading, grammar, summary
    private String model; // gpt-4o, gpt-4-turbo, gpt-4, gpt-3.5-turbo
    private String customPrompt;
    private String wordList; // CSV形式の誤字脱字リスト（mode=proofreadingの場合のみ使用）

    public CorrectionRequest() {
    }

    public CorrectionRequest(String text, String mode, String model, String customPrompt, String wordList) {
        this.text = text;
        this.mode = mode;
        this.model = model;
        this.customPrompt = customPrompt;
        this.wordList = wordList;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getCustomPrompt() {
        return customPrompt;
    }

    public void setCustomPrompt(String customPrompt) {
        this.customPrompt = customPrompt;
    }

    public String getWordList() {
        return wordList;
    }

    public void setWordList(String wordList) {
        this.wordList = wordList;
    }

    public static CorrectionRequestBuilder builder() {
        return new CorrectionRequestBuilder();
    }

    public static class CorrectionRequestBuilder {
        private String text;
        private String mode;
        private String model;
        private String customPrompt;
        private String wordList;

        CorrectionRequestBuilder() {
        }

        public CorrectionRequestBuilder text(String text) {
            this.text = text;
            return this;
        }

        public CorrectionRequestBuilder mode(String mode) {
            this.mode = mode;
            return this;
        }

        public CorrectionRequestBuilder model(String model) {
            this.model = model;
            return this;
        }

        public CorrectionRequestBuilder customPrompt(String customPrompt) {
            this.customPrompt = customPrompt;
            return this;
        }

        public CorrectionRequestBuilder wordList(String wordList) {
            this.wordList = wordList;
            return this;
        }

        public CorrectionRequest build() {
            return new CorrectionRequest(text, mode, model, customPrompt, wordList);
        }
    }
}