# TranscriptCleaner

## 概要

Microsoft Teams の自動生成トランスクリプト（議事録）は、しばしば漏字・脱字や誤字が含まれます。本ツール「TranscriptCleaner」は、アップロードされた議事録テキストと誤字脱字一覧（CSV）をもとに、OpenAI API を活用して高精度な訂正を行う Web アプリケーションです。

**主な特徴:**
- 🤖 **AI駆動の高精度訂正**: OpenAI GPTモデルによる自然な文章訂正
- 👥 **ユーザー管理**: ロールベースアクセス制御による安全な運用
- 🔐 **権限管理**: 細かい権限設定による柔軟なアクセス制御
- 📊 **使用状況追跡**: コストとトークン使用量の詳細な記録
- 🎯 **多機能対応**: 誤字脱字修正、文法訂正、要約機能

---

## 主な機能

### 📝 トランスクリプト訂正機能

#### 1. 誤字脱字修正モード
- CSVファイルで定義された単語集を使用して、よくある誤字脱字を自動修正
- カスタム単語リストの編集・管理機能
- Excel風のグリッド表示による直感的な編集

#### 2. 文法訂正モード
- OpenAI APIを使用して文法的な改善を実施
- 自然な日本語表現への変換
- ビジネス文書に適した文体調整

#### 3. 要約モード
- 長い議事録を要点を抑えた要約に変換
- 重要なポイントを見逃さない構造化された要約
- 会議の決定事項と次のアクションを明確化

### ⚙️ 管理機能（管理者専用）

#### 1. ユーザー管理
![ユーザー管理画面](./docs/images/user-management.png)

- **ユーザーアカウント管理**: 作成・編集・削除・有効化/無効化
- **ロール割り当て**: 各ユーザーに適切な権限レベルを設定
- **ステータス監視**: アクティブ/非アクティブユーザーの管理
- **一覧表示**: 検索・フィルタリング機能付きのユーザー一覧

#### 2. ロール管理
![ロール管理画面](./docs/images/role-management.png)

- **システムロール**: ADMIN、USER、GUEST、PREMIUMの事前定義ロール
- **カスタムロール**: 組織のニーズに応じたカスタムロール作成
- **権限設定**: 機能レベルでの細かい権限制御
- **ユーザー数表示**: 各ロールに割り当てられたユーザー数の確認

#### 3. 権限制御システム
- **ロールベースアクセス制御（RBAC）**: Spring Securityによる堅牢な認証・認可
- **機能レベル権限**: ユーザー管理、システム設定、データアクセス等の細かい制御
- **セッション管理**: 安全なログイン状態の維持と自動ログアウト

---

## 操作手順

本ツール「TranscriptCleaner」を使用して議事録を訂正する基本的な流れは以下の通りです。

1. **アプリケーションの起動:**

   * バックエンド（Spring Boot）を起動します。
     - コマンドラインで `./mvnw spring-boot:run` または `mvn spring-boot:run` を実行します。
   * フロントエンド（ReactJS）を起動します。
     - `frontend` ディレクトリで `npm install`、その後 `npm start` を実行します。
   * ブラウザで `http://localhost:3000` を開きます。
2. **APIキーの確認:**

   * バックエンドがAPIキーを必要とする場合は、`application.properties`等で設定します。
3. **処理モードの選択:**

   * 画面上で「誤字脱字修正」「文法訂正」「要約」などの処理モードを選択します。
4. **議事録ファイルのアップロード:**

   * UIのファイルアップロードボタンから、訂正したいトランスクリプトのテキストファイルをアップロードします。
5. **（誤字脱字修正モードの場合）誤字脱字リストの準備:**

   * CSVファイルをアップロード、またはテキストエリアで直接編集できます。
6. **モデルとカスタムプロンプトの設定（任意）:**

   * 使用するAIモデルや追加指示を設定できます。
7. **訂正の実行:**

   * 「訂正実行」ボタンをクリックし、処理を開始します。
8. **結果の確認と手修正:**

   * 訂正後のテキストが表示され、必要に応じて手修正が可能です。
   * 差分表示機能も利用できます。
9. **（任意）訂正結果を元に再訂正:**

   * 訂正後テキストを再度訂正前として利用できます。
10. **最終結果のダウンロード:**

    * 訂正済みの議事録をダウンロードできます。

**コストについて:**
OpenAI APIの利用にはコストが発生します。画面上で使用状況やコスト履歴を確認できます。

---

## 技術スタック

### フロントエンド
- **React 18**: モダンなUIライブラリ
- **Material-UI (MUI)**: 統一されたデザインシステム
- **Axios**: HTTP通信ライブラリ
- **diff**: テキスト差分表示機能

### バックエンド
- **Spring Boot 3**: Javaベースのマイクロサービスフレームワーク
- **Spring Security**: 認証・認可システム
- **Spring Data JPA**: データベースアクセス層
- **MySQL**: リレーショナルデータベース
- **OpenAI API**: AI駆動のテキスト処理

### データベース設計
```sql
-- 主要テーブル
user                 -- ユーザー管理
role                 -- ロール定義
user_role           -- ユーザー・ロール関連
correction_history  -- 訂正履歴とコスト追跡
session            -- セッション管理
word_list_history  -- 単語リスト履歴
```

---

## アーキテクチャ

```
┌─────────────────┐    HTTP/REST    ┌─────────────────┐
│   Frontend      │ ◄──────────────► │   Backend       │
│   (React)       │                 │   (Spring Boot) │
│                 │                 │                 │
│ ├─ User Mgmt    │                 │ ├─ Auth Service │
│ ├─ Role Mgmt    │                 │ ├─ User Service │
│ ├─ Transcript   │                 │ ├─ Role Service │
│ └─ Admin Panel  │                 │ └─ OpenAI API   │
└─────────────────┘                 └─────────────────┘
                                              │
                                              ▼
                                    ┌─────────────────┐
                                    │     MySQL       │
                                    │   Database      │
                                    └─────────────────┘
```

---

## フォルダ構成

```
TranscriptCleaner/
├── frontend/                          # React フロントエンド
│   ├── src/
│   │   ├── components/
│   │   │   ├── AdminPanelContent.js   # 管理機能メインパネル
│   │   │   ├── UserManagement.js      # ユーザー管理画面
│   │   │   ├── RoleManagement.js      # ロール管理画面
│   │   │   ├── TranscriptCleanerContent.js # 議事録訂正画面
│   │   │   ├── Sidebar.js             # ドロワー式サイドバー
│   │   │   ├── MainContent.js         # メインコンテンツ切り替え
│   │   │   └── Login.js               # ログイン画面
│   │   ├── services/
│   │   │   └── apiService.js          # API通信サービス
│   │   └── App.js                     # メインアプリケーション
│   └── package.json
├── backend/                           # Spring Boot バックエンド
│   ├── src/main/java/com/transcriptcleaner/
│   │   ├── controller/                # REST APIコントローラー
│   │   │   ├── ApiController.js       # 議事録訂正API
│   │   │   ├── AuthController.js      # 認証API
│   │   │   └── RoleController.js      # ロール管理API
│   │   ├── model/                     # エンティティクラス
│   │   │   ├── User.java              # ユーザーエンティティ
│   │   │   ├── Role.java              # ロールエンティティ
│   │   │   └── UserRole.java          # ユーザーロール関連
│   │   ├── repository/                # データアクセス層
│   │   ├── service/                   # ビジネスロジック層
│   │   │   ├── RoleService.java       # ロール管理サービス
│   │   │   └── UserDetailsServiceImpl.java # 認証サービス
│   │   └── config/
│   │       └── SecurityConfig.java    # Spring Security設定
│   └── src/main/resources/
│       ├── application.properties     # アプリケーション設定
│       ├── schema.sql                 # データベーススキーマ
│       ├── data.sql                   # 初期データ
│       ├── role_schema.sql            # ロール機能スキーマ
│       └── maintenance.sql            # メンテナンス用クエリ
├── transcriptdata/                    # サンプルデータ
│   ├── sample_transcript.txt          # サンプル議事録
│   └── correction_words.csv           # 誤字脱字一覧
├── docs/                              # ドキュメント
│   └── images/                        # スクリーンショット
│       ├── user-management.png        # ユーザー管理画面
│       └── role-management.png        # ロール管理画面
└── README.md
```

---

## 要件整理

### 基本要件

- 入力：トランスクリプト（テキスト）、訂正単語集（CSV）
- 出力：訂正済み議事録
- UI：ファイルアップロード、訂正実行、結果表示・ダウンロード
- 訂正方法：OpenAI API ＋単語集
- セットアップ・実行手順・API キー設定方法・サンプルデータ利用方法も含めること

### モジュール要件

- フロントエンドとバックエンドの分離
- REST APIによる通信
- 明確なデータ構造定義（例：JSON）
- テストコード実装：重要ロジックにはユニットテスト

---

## セットアップ手順

### 1. 前提条件
- **Java 17+**: Spring Boot 3.x の要件
- **Node.js 16+**: React アプリケーションの実行
- **MySQL 8.0+**: データベースサーバー
- **OpenAI API キー**: AI機能の利用

### 2. データベースセットアップ
```bash
# MySQLにログイン
mysql -u root -p

# データベース作成
CREATE DATABASE transcriptcleaner CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# スキーマとデータの初期化
mysql -u root -p transcriptcleaner < backend/src/main/resources/schema.sql
mysql -u root -p transcriptcleaner < backend/src/main/resources/role_schema.sql
mysql -u root -p transcriptcleaner < backend/src/main/resources/data.sql
```

### 3. バックエンドセットアップ
```bash
cd backend

# 環境変数でOpenAI APIキーを設定（セキュリティ重要）
export OPENAI_API_KEY="your-openai-api-key-here"

# application.propertiesでデータベース接続情報を設定

# アプリケーション起動
./mvnw spring-boot:run
# または
mvn spring-boot:run
```

**⚠️ セキュリティ注意事項:**
- OpenAI APIキーは必ず環境変数で設定してください
- APIキーをソースコードやconfig.jsonに保存しないでください
- 本番環境では適切な環境変数管理システムを使用してください

### 4. フロントエンドセットアップ
```bash
cd frontend

# 依存関係のインストール
npm install

# 開発サーバー起動
npm start
```

### 5. 初期ユーザー
以下のユーザーでログインできます：
- **管理者**: `admin` / `admin123`
- **一般ユーザー**: `testuser` / `test123`
- **ゲスト**: `demo` / `demo123`

---

## 実行方法

1. バックエンド（Spring Boot）を起動
2. フロントエンド（ReactJS）を起動
3. ブラウザで `http://localhost:3000` を開く
4. UIからファイルアップロードや訂正実行などを操作

---

## UI 詳細要件

- 誤字脱字修正モード
  ![誤字脱字修正モード](./docs/images/proofreading-mode.png)
- 文法訂正モード
  ![文法訂正モード](./docs/images/grammar-mode.png)
- 要約モード
  ![要約モード](./docs/images/summary-mode.png)

---

- 左側サイドバー上部に OpenAI モデル選択（コンボボックス、gpt-4o, gpt-4-turbo, gpt-4, gpt-3.5-turboなどを選択可能）
- 左側サイドバー上部にカスタムプロンプト入力欄（処理モードに応じた指示を入力）
- 左側サイドバーに訂正前の議事録（TXT 限定）アップロードボタン
- 左側サイドバーに誤字脱字一覧表示テキストエリア（CSV 形式表示・編集可、空の場合は「誤,正」をプレースホルダとして表示）
- 左側サイドバーに誤字脱字一覧（CSV 限定）アップロードボタン
- 左側サイドバー下部に訂正実行ボタン
- メイン画面の左列に訂正前の議事録（編集不可）、右列に訂正後の議事録（手修正可能なテキストエリア）を表示
- メイン画面のテキストエリア下部に差分表示のチェックボックスを配置し、チェックするとその下に詳細差分を表示
- メイン画面下部に、訂正後テキスト（右列）を訂正前テキスト（左列）にコピーするボタンを設置
- メイン画面下部に「最終修正結果をダウンロード」ボタン
- ユーザーが何度も OpenAI に再訂正を依頼できる運用を想定

---

## 構造設計

- フロントエンド（ReactJS）：
  - ファイルアップロード、設定、実行ボタン、結果表示、差分表示、ダウンロードなどのUI
  - REST APIクライアント
- バックエンド（Spring Boot）：
  - ファイル受信、OpenAI API連携、訂正処理、CSV解析、コスト管理などの業務ロジック
  - REST APIエンドポイント
- サンプルデータ：`transcriptdata/` 配下

---

## 開発ロードマップ

### ✅ 完了済み機能
- ✅ **基本的な議事録訂正機能**: 誤字脱字修正、文法訂正、要約
- ✅ **ユーザー認証システム**: Spring Security による安全な認証
- ✅ **ロールベースアクセス制御**: 細かい権限管理システム
- ✅ **ユーザー管理機能**: 管理者による包括的なユーザー管理
- ✅ **ロール管理機能**: カスタムロールと権限設定
- ✅ **差分表示機能**: 訂正前後の詳細な比較表示
- ✅ **ドロワー式UI**: 直感的なナビゲーションシステム

### 🚧 開発中・計画中
- 🔄 **API連携の完全実装**: フロントエンドとバックエンドの完全統合
- 📊 **統計・レポート機能**: 使用状況とコスト分析ダッシュボード
- 🔔 **通知システム**: 処理完了やエラーの通知機能
- 📱 **レスポンシブデザイン**: モバイル対応の改善
- 🌐 **多言語対応**: 英語・日本語以外の言語サポート
- 🔒 **セキュリティ強化**: 2FA、監査ログ、セッション管理の改善

### 🎯 将来の拡張計画
- **バッチ処理機能**: 大量ファイルの一括処理
- **API外部連携**: Teams、Slack等との直接連携
- **カスタムAIモデル**: 組織専用の訂正モデル学習
- **ワークフロー機能**: 承認プロセスと段階的処理
- **クラウド対応**: AWS/Azure等でのスケーラブル運用

---

## ライセンス

このプロジェクトはMITライセンスの下で公開されています。

## 貢献

プルリクエストやイシューの報告を歓迎します。開発に参加される場合は、以下のガイドラインに従ってください：

1. フォークしてブランチを作成
2. 変更を実装してテストを追加
3. コミットメッセージは明確に記述
4. プルリクエストを作成

## サポート

質問や問題がある場合は、GitHubのIssuesページでお知らせください。
