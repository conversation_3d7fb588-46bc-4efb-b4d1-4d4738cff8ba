package com.transcriptcleaner.model;

public class CorrectionResponse {
    private String originalText;
    private String correctedText;
    private double cost;
    private int tokenCount;
    private String model;
    private String mode;
    private long processingTimeMs;
    private String error;

    public CorrectionResponse() {
    }

    public CorrectionResponse(String originalText, String correctedText, double cost, int tokenCount, String model, String mode, long processingTimeMs, String error) {
        this.originalText = originalText;
        this.correctedText = correctedText;
        this.cost = cost;
        this.tokenCount = tokenCount;
        this.model = model;
        this.mode = mode;
        this.processingTimeMs = processingTimeMs;
        this.error = error;
    }

    public String getOriginalText() {
        return originalText;
    }

    public void setOriginalText(String originalText) {
        this.originalText = originalText;
    }

    public String getCorrectedText() {
        return correctedText;
    }

    public void setCorrectedText(String correctedText) {
        this.correctedText = correctedText;
    }

    public double getCost() {
        return cost;
    }

    public void setCost(double cost) {
        this.cost = cost;
    }

    public int getTokenCount() {
        return tokenCount;
    }

    public void setTokenCount(int tokenCount) {
        this.tokenCount = tokenCount;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public long getProcessingTimeMs() {
        return processingTimeMs;
    }

    public void setProcessingTimeMs(long processingTimeMs) {
        this.processingTimeMs = processingTimeMs;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public static CorrectionResponseBuilder builder() {
        return new CorrectionResponseBuilder();
    }

    public static class CorrectionResponseBuilder {
        private String originalText;
        private String correctedText;
        private double cost;
        private int tokenCount;
        private String model;
        private String mode;
        private long processingTimeMs;
        private String error;

        CorrectionResponseBuilder() {
        }

        public CorrectionResponseBuilder originalText(String originalText) {
            this.originalText = originalText;
            return this;
        }

        public CorrectionResponseBuilder correctedText(String correctedText) {
            this.correctedText = correctedText;
            return this;
        }

        public CorrectionResponseBuilder cost(double cost) {
            this.cost = cost;
            return this;
        }

        public CorrectionResponseBuilder tokenCount(int tokenCount) {
            this.tokenCount = tokenCount;
            return this;
        }

        public CorrectionResponseBuilder model(String model) {
            this.model = model;
            return this;
        }

        public CorrectionResponseBuilder mode(String mode) {
            this.mode = mode;
            return this;
        }

        public CorrectionResponseBuilder processingTimeMs(long processingTimeMs) {
            this.processingTimeMs = processingTimeMs;
            return this;
        }

        public CorrectionResponseBuilder error(String error) {
            this.error = error;
            return this;
        }

        public CorrectionResponse build() {
            return new CorrectionResponse(originalText, correctedText, cost, tokenCount, model, mode, processingTimeMs, error);
        }
    }
}