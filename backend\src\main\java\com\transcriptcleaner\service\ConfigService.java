package com.transcriptcleaner.service;

import com.transcriptcleaner.model.WordListInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ConfigService {

    @Value("${app.config.directory:config}")
    private String configDirectory;

    @Value("${app.word-history.file:config/word_history.json}")
    private String wordHistoryFile;

    @Value("${openai.api.key}")
    private String defaultApiKey;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private List<WordListInfo> wordLists = new ArrayList<>();
    
    // 設定を保持するマップ
    private Map<String, Object> config = new HashMap<>();
    
    // 設定ファイルのパス
    private String configFile;

    @PostConstruct
    public void init() {
        // configFileパスを初期化
        configFile = configDirectory + File.separator + "config.json";
        createConfigDirectoryIfNotExists();
        loadConfig();
        loadWordLists();
    }

    private void createConfigDirectoryIfNotExists() {
        try {
            Path directory = Paths.get(configDirectory);
            if (!Files.exists(directory)) {
                Files.createDirectories(directory);
                log.info("設定ディレクトリを作成しました: {}", configDirectory);
            }

            // 単語履歴ファイルのディレクトリも作成
            Path wordHistoryDir = Paths.get(wordHistoryFile).getParent();
            if (wordHistoryDir != null && !Files.exists(wordHistoryDir)) {
                Files.createDirectories(wordHistoryDir);
                log.info("単語履歴ディレクトリを作成しました: {}", wordHistoryDir);
            }
        } catch (IOException e) {
            log.error("ディレクトリの作成に失敗しました", e);
        }
    }

    private void loadConfig() {
        // コスト情報のみを管理（APIキーは環境変数のみ）
        File file = new File(configFile);
        if (file.exists()) {
            try {
                config = objectMapper.readValue(file, objectMapper.getTypeFactory().constructMapType(HashMap.class, String.class, Object.class));
                log.info("設定を読み込みました: {}", configFile);
            } catch (IOException e) {
                log.error("設定ファイルの読み込みに失敗しました", e);
                config = new HashMap<>();
                config.put("totalCost", 0.0);
            }
        } else {
            // デフォルト設定（コストのみ）
            config = new HashMap<>();
            config.put("totalCost", 0.0);
            saveConfig();
        }
    }

    private void saveConfig() {
        try {
            // ディレクトリが存在することを確認
            File file = new File(configFile);
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            objectMapper.writeValue(file, config);
            log.info("設定を保存しました: {}", configFile);
        } catch (IOException e) {
            log.error("設定ファイルの保存に失敗しました: {}", configFile, e);
            // ファイル保存に失敗してもアプリケーションは継続
        }
    }

    private void loadWordLists() {
        File file = new File(wordHistoryFile);
        if (file.exists()) {
            try {
                wordLists = objectMapper.readValue(file,
                        objectMapper.getTypeFactory().constructCollectionType(List.class, WordListInfo.class));
                log.info("単語リストを読み込みました: {}", wordHistoryFile);
            } catch (IOException e) {
                log.error("単語リストファイルの読み込みに失敗しました", e);
                wordLists = new ArrayList<>();
            }
        } else {
            wordLists = new ArrayList<>();
            saveWordLists();
        }
    }

    private void saveWordLists() {
        try {
            // ディレクトリが存在することを確認
            File file = new File(wordHistoryFile);
            File parentDir = file.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            objectMapper.writeValue(file, wordLists);
            log.info("単語リストを保存しました: {}", wordHistoryFile);
        } catch (IOException e) {
            log.error("単語リストファイルの保存に失敗しました: {}", wordHistoryFile, e);
            // ファイル保存に失敗してもアプリケーションは継続
        }
    }

    public String getApiKey() {
        // Only get from environment variables for security
        String envKey = System.getenv("OPENAI_API_KEY");
        if (envKey != null && !envKey.isEmpty()) {
            return envKey;
        }
        // Fallback to application.properties
        return defaultApiKey;
    }

    public double getTotalCost() {
        Object value = config.getOrDefault("totalCost", 0.0);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return 0.0;
    }

    public void addCost(double cost) {
        try {
            double currentCost = getTotalCost();
            config.put("totalCost", currentCost + cost);
            saveConfig();
            log.debug("コストを追加しました: {} (合計: {})", cost, currentCost + cost);
        } catch (Exception e) {
            log.error("コストの追加に失敗しました: {}", cost, e);
            // コスト追加に失敗してもアプリケーションは継続
        }
    }

    public void resetCost() {
        config.put("totalCost", 0.0);
        saveConfig();
    }

    public List<WordListInfo> getWordLists() {
        return new ArrayList<>(wordLists);
    }

    public void saveWordList(String name, String wordList) {
        WordListInfo existingList = wordLists.stream()
                .filter(wl -> wl.getName().equals(name))
                .findFirst()
                .orElse(null);

        long currentTime = System.currentTimeMillis();

        if (existingList != null) {
            existingList.setWordList(wordList);
            existingList.setUpdatedAt(currentTime);
        } else {
            WordListInfo newList = WordListInfo.builder()
                    .name(name)
                    .wordList(wordList)
                    .createdAt(currentTime)
                    .updatedAt(currentTime)
                    .build();
            wordLists.add(newList);
        }

        saveWordLists();
    }

    public boolean deleteWordList(String name) {
        boolean removed = wordLists.removeIf(wl -> wl.getName().equals(name));
        if (removed) {
            saveWordLists();
        }
        return removed;
    }

}
