package com.transcriptcleaner.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;

@Entity
@Table(name = "user_role")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserRole {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "role_id", nullable = false)
    private Role role;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "assigned_by")
    private User assignedBy;

    @Column(name = "assigned_at", nullable = false, columnDefinition = "datetime default current_timestamp")
    private LocalDateTime assignedAt;

    @Column(name = "expires_at")
    private LocalDateTime expiresAt;

    @Column(name = "is_active", nullable = false, columnDefinition = "tinyint(1) default 1")
    private boolean isActive = true;

    // ビジネスロジック用メソッド
    public boolean isExpired() {
        return expiresAt != null && expiresAt.isBefore(LocalDateTime.now());
    }

    public boolean isCurrentlyActive() {
        return isActive && !isExpired();
    }

    public boolean isPermanent() {
        return expiresAt == null;
    }

    public long getDaysUntilExpiry() {
        if (expiresAt == null) {
            return Long.MAX_VALUE; // 無期限
        }
        return java.time.temporal.ChronoUnit.DAYS.between(LocalDateTime.now(), expiresAt);
    }

    public String getStatusDescription() {
        if (!isActive) {
            return "無効";
        }
        if (isExpired()) {
            return "期限切れ";
        }
        if (isPermanent()) {
            return "無期限";
        }
        long daysLeft = getDaysUntilExpiry();
        if (daysLeft <= 0) {
            return "期限切れ";
        } else if (daysLeft <= 7) {
            return "まもなく期限切れ (" + daysLeft + "日)";
        } else {
            return "有効 (" + daysLeft + "日)";
        }
    }

    // JPA ライフサイクルコールバック
    @PrePersist
    protected void onCreate() {
        if (assignedAt == null) {
            assignedAt = LocalDateTime.now();
        }
    }

    // 便利なファクトリーメソッド
    public static UserRole createPermanentAssignment(User user, Role role, User assignedBy) {
        return UserRole.builder()
                .user(user)
                .role(role)
                .assignedBy(assignedBy)
                .assignedAt(LocalDateTime.now())
                .expiresAt(null)
                .isActive(true)
                .build();
    }

    public static UserRole createTemporaryAssignment(User user, Role role, User assignedBy, LocalDateTime expiresAt) {
        return UserRole.builder()
                .user(user)
                .role(role)
                .assignedBy(assignedBy)
                .assignedAt(LocalDateTime.now())
                .expiresAt(expiresAt)
                .isActive(true)
                .build();
    }

    // toString メソッドのオーバーライド
    @Override
    public String toString() {
        return "UserRole{" +
                "id=" + id +
                ", userId=" + (user != null ? user.getId() : null) +
                ", roleId=" + (role != null ? role.getId() : null) +
                ", roleName=" + (role != null ? role.getName() : null) +
                ", assignedAt=" + assignedAt +
                ", expiresAt=" + expiresAt +
                ", isActive=" + isActive +
                '}';
    }

    // equals と hashCode のオーバーライド
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof UserRole)) return false;
        UserRole userRole = (UserRole) o;
        return user != null && role != null && 
               user.equals(userRole.user) && role.equals(userRole.role);
    }

    @Override
    public int hashCode() {
        int result = user != null ? user.hashCode() : 0;
        result = 31 * result + (role != null ? role.hashCode() : 0);
        return result;
    }
}
