package com.transcriptcleaner.service;

import com.transcriptcleaner.model.CorrectionRequest;
import com.transcriptcleaner.model.CorrectionResponse;
import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatCompletionResult;
import com.theokanning.openai.completion.chat.ChatMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class OpenAiService {

    @Value("${openai.api.key:}")
    private String apiKey;

    private final ConfigService configService;
    private final Map<String, Double> modelCosts = new HashMap<>();

    public OpenAiService(ConfigService configService) {
        this.configService = configService;
        // モデルごとのコスト設定（1000トークンあたりのUSD）
        modelCosts.put("gpt-4o", 0.005);
        modelCosts.put("gpt-4-turbo", 0.01);
        modelCosts.put("gpt-4", 0.03);
        modelCosts.put("gpt-3.5-turbo", 0.0015);
    }

    public CorrectionResponse correctText(CorrectionRequest request) {
        long startTime = System.currentTimeMillis();
        String userApiKey = configService.getApiKey();
        
        if (userApiKey == null || userApiKey.isEmpty()) {
            return CorrectionResponse.builder()
                    .error("OpenAI APIキーが設定されていません。")
                    .build();
        }

        try {
            com.theokanning.openai.service.OpenAiService service = new com.theokanning.openai.service.OpenAiService(userApiKey, Duration.ofSeconds(60));
            
            List<ChatMessage> messages = new ArrayList<>();
            messages.add(new ChatMessage("system", getSystemPrompt(request)));
            messages.add(new ChatMessage("user", request.getText()));

            ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
                    .model(request.getModel())
                    .messages(messages)
                    .temperature(0.7)
                    .build();

            ChatCompletionResult result = service.createChatCompletion(completionRequest);
            String correctedText = result.getChoices().get(0).getMessage().getContent();
            int totalTokens = (int) result.getUsage().getTotalTokens(); // 明示的にintにキャスト
            double cost = calculateCost(request.getModel(), totalTokens);
            
            // コスト履歴を更新
            configService.addCost(cost);

            long processingTime = System.currentTimeMillis() - startTime;
            
            CorrectionResponse response = new CorrectionResponse();
            response.setOriginalText(request.getText());
            response.setCorrectedText(correctedText);
            response.setCost(cost);
            response.setTokenCount(totalTokens);
            response.setModel(request.getModel());
            response.setMode(request.getMode());
            response.setProcessingTimeMs(processingTime);
            return response;
            
        } catch (Exception e) {
            log.error("OpenAI API呼び出し中にエラーが発生しました", e);
            CorrectionResponse response = new CorrectionResponse();
            response.setError("OpenAI API呼び出し中にエラーが発生しました: " + e.getMessage());
            return response;
        }
    }

    private String getSystemPrompt(CorrectionRequest request) {
        String basePrompt;
        
        switch (request.getMode()) {
            case "proofreading": {
                basePrompt = "あなたは誤字脱字訂正専用のAIです。絶対に要約、追加、削除、言い換え、書式変更、文体変更、内容の修正、段落の統合・分割、語順の変更、その他の編集は行わず、\n"+
                    "誤字脱字リストに基づく訂正のみを行ってください。誤字脱字リストに該当しない箇所は一切変更せず、元のテキストをそのまま残してください。\n"+
                    "訂正リストにない箇所は絶対に修正しないでください。人間による最終確認・修正が必須です。\n";
                
                // 単語リストが提供されている場合は追加
                if (request.getWordList() != null && !request.getWordList().isEmpty()) {
                    basePrompt += "\n\n特に、以下のリストにある単語は優先的に修正してください：\n" + 
                            request.getWordList();
                }
                break;
            }
                
            case "grammar":
                basePrompt = "あなたは日本語の文章を自然で文法的に正しく校正するAIです。\n" +
                "以下の指示に従って、提供されたテキストを修正してください。以下の指示に従って、提供されたテキストを修正してください。\n"+
                "1. 文法的な誤りを修正します。\n"+
                "2. 不自然な表現をより自然な日本語、もしくは英語に修正します。\n"+
                "3. 誤字脱字があれば修正します。"+
                "4. 元の文章の意味や主要な情報を保持し、勝手に内容を追加したり削除したりしないでください。"+
                "5. 文体は元のテキストに合わせてください。";

                // 単語リストが提供されている場合は追加
                if (request.getWordList() != null && !request.getWordList().isEmpty()) {
                    basePrompt += "\n\n特に、以下のリストにある単語は優先的に修正してください：\n" + 
                            request.getWordList();
                }                break;
                
            case "summary":
                basePrompt = "あなたは提供された日本語のテキストを要約するAIです。\n" +
                        "以下の指示に従って、テキストの要点をまとめてください。\n" +
                        "1. テキスト全体の主要なトピックと結論を把握します。\n" +
                        "2. 重要な情報を抽出し、冗長な部分や詳細は省略します。\n" +
                        "3. 元のテキストの意図を正確に反映した要約を作成します。\n";
                break;
                
            default:
                basePrompt = "以下のテキストを校正し、読みやすく自然な日本語にしてください。";
        }
        
        // カスタムプロンプトがある場合は追加
        if (request.getCustomPrompt() != null && !request.getCustomPrompt().isEmpty()) {
            basePrompt += "\n\n" + request.getCustomPrompt();
        }
        
        return basePrompt;
    }

    private double calculateCost(String model, int tokens) {
        Double costPer1000Tokens = modelCosts.getOrDefault(model, 0.0);
        return (tokens / 1000.0) * costPer1000Tokens;
    }

    public boolean isApiKeyValid(String apiKey) {
        try {
            com.theokanning.openai.service.OpenAiService service = new com.theokanning.openai.service.OpenAiService(apiKey, Duration.ofSeconds(10));
            service.listModels();
            return true;
        } catch (Exception e) {
            log.error("APIキーの検証に失敗しました", e);
            return false;
        }
    }
}