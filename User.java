
@Entity
@Table(name = "user")
@Data
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String username;

    @Column(name = "password_hash", nullable = false)
    private String passwordHash;

    // 添加 email 字段
    @Column(nullable = false)
    private String email;

    // 添加 created_at 字段
    @Column(nullable = false, columnDefinition = "datetime default current_timestamp")
    private LocalDateTime createdAt;

    // 添加 updated_at 字段
    @Column(nullable = false, columnDefinition = "datetime default current_timestamp on update current_timestamp")
    private LocalDateTime updatedAt;

    // 添加 is_active 字段
    @Column(nullable = false, columnDefinition = "tinyint(1) default 1")
    private boolean isActive;
}
