-- TranscriptCleaner Database Schema
-- データベース: transcriptcleaner
-- 文字セット: utf8mb4
-- 照合順序: utf8mb4_unicode_ci

-- データベース作成（必要に応じて）
-- CREATE DATABASE IF NOT EXISTS transcriptcleaner CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- USE transcriptcleaner;

-- ============================================================================
-- 1. user テーブル - ユーザー管理
-- ============================================================================
CREATE TABLE IF NOT EXISTS user (
    id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT 'ユーザーID',
    username VARCHAR(64) NOT NULL UNIQUE COMMENT 'ユーザー名（ログイン用）',
    email VARCHAR(128) NOT NULL UNIQUE COMMENT 'メールアドレス',
    password_hash VARCHAR(255) NOT NULL COMMENT 'パスワードハッシュ（BCrypt）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '作成日時',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新日時',
    is_active TINYINT(1) DEFAULT 1 COMMENT 'アクティブフラグ（1:有効, 0:無効）',
    
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='ユーザー管理テーブル';

-- ============================================================================
-- 2. correction_history テーブル - 訂正履歴管理
-- ============================================================================
CREATE TABLE IF NOT EXISTS correction_history (
    id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT '履歴ID',
    user_id BIGINT(20) NOT NULL COMMENT 'ユーザーID',
    original_text TEXT NOT NULL COMMENT '元のテキスト',
    corrected_text TEXT NOT NULL COMMENT '訂正後のテキスト',
    mode VARCHAR(32) NOT NULL COMMENT '処理モード（proofreading, grammar, summary）',
    model VARCHAR(32) NOT NULL COMMENT 'AIモデル（gpt-4, gpt-4-turbo, gpt-3.5-turbo等）',
    cost DOUBLE DEFAULT NULL COMMENT 'API使用コスト（USD）',
    token_count INT(11) DEFAULT NULL COMMENT 'トークン数',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '作成日時',
    
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_mode (mode),
    INDEX idx_model (model),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='訂正履歴管理テーブル';

-- ============================================================================
-- 3. session テーブル - セッション管理
-- ============================================================================
CREATE TABLE IF NOT EXISTS session (
    id VARCHAR(128) PRIMARY KEY COMMENT 'セッションID',
    user_id BIGINT(20) NOT NULL COMMENT 'ユーザーID',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '作成日時',
    expires_at DATETIME NOT NULL COMMENT '有効期限',
    
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='セッション管理テーブル';

-- ============================================================================
-- 4. word_list_history テーブル - 単語リスト履歴管理
-- ============================================================================
CREATE TABLE IF NOT EXISTS word_list_history (
    id BIGINT(20) AUTO_INCREMENT PRIMARY KEY COMMENT '単語リストID',
    user_id BIGINT(20) NOT NULL COMMENT 'ユーザーID',
    name VARCHAR(64) NOT NULL COMMENT '単語リスト名',
    word_list TEXT NOT NULL COMMENT '単語リスト（CSV形式）',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '作成日時',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '更新日時',
    
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_name (name),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='単語リスト履歴管理テーブル';

-- ============================================================================
-- サンプルデータ挿入（開発用）
-- ============================================================================

-- デフォルトユーザー作成（パスワード: password123）
-- BCryptハッシュ: $2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM7lbdxOmCrliKpfyR.m
INSERT IGNORE INTO user (username, email, password_hash, is_active) VALUES 
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM7lbdxOmCrliKpfyR.m', 1),
('testuser', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM7lbdxOmCrliKpfyR.m', 1);

-- サンプル単語リスト
INSERT IGNORE INTO word_list_history (user_id, name, word_list) VALUES 
(1, 'デフォルト誤字脱字リスト', '誤字,正字\nてすと,テスト\nかいぎ,会議\nぎじろく,議事録'),
(1, 'IT用語リスト', 'API,API\nデータベース,データベース\nアプリケーション,アプリケーション');

-- ============================================================================
-- 便利なクエリ例
-- ============================================================================

-- /* ユーザー別訂正履歴の統計 */
-- SELECT 
--     u.username,
--     COUNT(ch.id) as correction_count,
--     SUM(ch.cost) as total_cost,
--     AVG(ch.token_count) as avg_tokens,
--     ch.mode,
--     ch.model
-- FROM user u
-- LEFT JOIN correction_history ch ON u.id = ch.user_id
-- WHERE u.is_active = 1
-- GROUP BY u.id, ch.mode, ch.model
-- ORDER BY total_cost DESC;

-- /* 期限切れセッションの削除 */
-- DELETE FROM session WHERE expires_at < NOW();

-- /* ユーザー別単語リスト一覧 */
-- SELECT 
--     u.username,
--     wlh.name as word_list_name,
--     wlh.created_at,
--     wlh.updated_at
-- FROM user u
-- JOIN word_list_history wlh ON u.id = wlh.user_id
-- WHERE u.is_active = 1
-- ORDER BY wlh.updated_at DESC;
