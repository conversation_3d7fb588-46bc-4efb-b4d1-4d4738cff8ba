package com.transcriptcleaner.model;

public class WordListInfo {
    private String name;
    private String wordList;
    private long createdAt;
    private long updatedAt;

    public WordListInfo() {
    }

    public WordListInfo(String name, String wordList, long createdAt, long updatedAt) {
        this.name = name;
        this.wordList = wordList;
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getWordList() {
        return wordList;
    }

    public void setWordList(String wordList) {
        this.wordList = wordList;
    }

    public long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }

    public long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public static WordListInfoBuilder builder() {
        return new WordListInfoBuilder();
    }

    public static class WordListInfoBuilder {
        private String name;
        private String wordList;
        private long createdAt;
        private long updatedAt;

        WordListInfoBuilder() {
        }

        public WordListInfoBuilder name(String name) {
            this.name = name;
            return this;
        }

        public WordListInfoBuilder wordList(String wordList) {
            this.wordList = wordList;
            return this;
        }

        public WordListInfoBuilder createdAt(long createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        public WordListInfoBuilder updatedAt(long updatedAt) {
            this.updatedAt = updatedAt;
            return this;
        }

        public WordListInfo build() {
            return new WordListInfo(name, wordList, createdAt, updatedAt);
        }
    }
}