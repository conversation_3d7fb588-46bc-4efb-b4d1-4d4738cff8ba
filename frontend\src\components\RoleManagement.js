import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControlLabel,
  Checkbox,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Edit,
  Delete,
  Add,
  Refresh,
  ExpandMore,
} from '@mui/icons-material';

function RoleManagement({ language }) {
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [editingRole, setEditingRole] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    displayName: '',
    description: '',
    permissions: {}
  });

  // 権限定義
  const permissionCategories = {
    user: {
      label: language === 'ja' ? 'ユーザー管理' : 'User Management',
      permissions: {
        user_management: language === 'ja' ? 'ユーザー管理' : 'User Management',
        view_all_data: language === 'ja' ? '全データ閲覧' : 'View All Data',
        export_data: language === 'ja' ? 'データエクスポート' : 'Export Data',
      }
    },
    system: {
      label: language === 'ja' ? 'システム管理' : 'System Management',
      permissions: {
        role_management: language === 'ja' ? 'ロール管理' : 'Role Management',
        system_settings: language === 'ja' ? 'システム設定' : 'System Settings',
        api_key_management: language === 'ja' ? 'APIキー管理' : 'API Key Management',
        cost_management: language === 'ja' ? 'コスト管理' : 'Cost Management',
      }
    },
    features: {
      label: language === 'ja' ? '機能' : 'Features',
      permissions: {
        text_correction: language === 'ja' ? 'テキスト訂正' : 'Text Correction',
        word_list_management: language === 'ja' ? '単語リスト管理' : 'Word List Management',
        view_own_history: language === 'ja' ? '履歴閲覧' : 'View Own History',
        download_results: language === 'ja' ? '結果ダウンロード' : 'Download Results',
        advanced_models: language === 'ja' ? '高度なモデル' : 'Advanced Models',
        batch_processing: language === 'ja' ? 'バッチ処理' : 'Batch Processing',
      }
    }
  };

  // ラベル定義
  const labels = {
    ja: {
      title: 'ロール管理',
      addRole: 'ロール追加',
      refresh: '更新',
      name: 'ロール名',
      displayName: '表示名',
      description: '説明',
      permissions: '権限',
      userCount: 'ユーザー数',
      actions: '操作',
      edit: '編集',
      delete: '削除',
      save: '保存',
      cancel: 'キャンセル',
      addNewRole: '新規ロール追加',
      editRole: 'ロール編集',
      confirmDelete: 'このロールを削除しますか？',
      loading: '読み込み中...',
      noRoles: 'ロールが見つかりません',
      systemRole: 'システムロール',
      customRole: 'カスタムロール',
    },
    en: {
      title: 'Role Management',
      addRole: 'Add Role',
      refresh: 'Refresh',
      name: 'Role Name',
      displayName: 'Display Name',
      description: 'Description',
      permissions: 'Permissions',
      userCount: 'User Count',
      actions: 'Actions',
      edit: 'Edit',
      delete: 'Delete',
      save: 'Save',
      cancel: 'Cancel',
      addNewRole: 'Add New Role',
      editRole: 'Edit Role',
      confirmDelete: 'Are you sure you want to delete this role?',
      loading: 'Loading...',
      noRoles: 'No roles found',
      systemRole: 'System Role',
      customRole: 'Custom Role',
    }
  };
  const t = labels[language] || labels['ja'];

  // ロール一覧を取得
  const fetchRoles = async () => {
    setLoading(true);
    try {
      // TODO: 実際のAPI呼び出しに置き換え
      // const response = await fetch('/api/roles');
      // const data = await response.json();
      
      // モックデータ
      const mockRoles = [
        {
          id: 1,
          name: 'ADMIN',
          displayName: 'システム管理者',
          description: 'システム全体の管理権限を持つ',
          userCount: 1,
          isSystemRole: true,
          permissions: {
            user_management: true,
            role_management: true,
            system_settings: true,
            view_all_data: true,
            export_data: true,
            text_correction: true,
            word_list_management: true,
            view_own_history: true,
            download_results: true,
            advanced_models: true,
            api_key_management: true,
            cost_management: true,
          }
        },
        {
          id: 2,
          name: 'USER',
          displayName: '一般ユーザー',
          description: '基本的な機能を利用できる',
          userCount: 5,
          isSystemRole: true,
          permissions: {
            text_correction: true,
            word_list_management: true,
            view_own_history: true,
            download_results: true,
          }
        },
        {
          id: 3,
          name: 'GUEST',
          displayName: 'ゲストユーザー',
          description: '限定的な機能のみ利用可能',
          userCount: 2,
          isSystemRole: true,
          permissions: {
            text_correction: true,
            download_results: true,
          }
        }
      ];
      
      setRoles(mockRoles);
      setError('');
    } catch (err) {
      setError('ロール一覧の取得に失敗しました');
      console.error('Error fetching roles:', err);
    } finally {
      setLoading(false);
    }
  };

  // 初期データ読み込み
  useEffect(() => {
    fetchRoles();
  }, []);

  // ダイアログを開く
  const handleOpenDialog = (role = null) => {
    if (role) {
      setEditingRole(role);
      setFormData({
        name: role.name,
        displayName: role.displayName,
        description: role.description,
        permissions: role.permissions || {}
      });
    } else {
      setEditingRole(null);
      setFormData({
        name: '',
        displayName: '',
        description: '',
        permissions: {}
      });
    }
    setOpenDialog(true);
  };

  // ダイアログを閉じる
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingRole(null);
    setFormData({
      name: '',
      displayName: '',
      description: '',
      permissions: {}
    });
  };

  // 権限の変更
  const handlePermissionChange = (permission, checked) => {
    setFormData({
      ...formData,
      permissions: {
        ...formData.permissions,
        [permission]: checked
      }
    });
  };

  // ロール保存
  const handleSaveRole = async () => {
    try {
      // TODO: 実際のAPI呼び出しに置き換え
      if (editingRole) {
        // 更新
        console.log('Updating role:', editingRole.id, formData);
      } else {
        // 新規作成
        console.log('Creating role:', formData);
      }
      
      handleCloseDialog();
      fetchRoles(); // リストを更新
    } catch (err) {
      setError('ロールの保存に失敗しました');
      console.error('Error saving role:', err);
    }
  };

  // ロール削除
  const handleDeleteRole = async (roleId, isSystemRole) => {
    if (isSystemRole) {
      setError('システムロールは削除できません');
      return;
    }
    
    if (window.confirm(t.confirmDelete)) {
      try {
        // TODO: 実際のAPI呼び出しに置き換え
        console.log('Deleting role:', roleId);
        fetchRoles(); // リストを更新
      } catch (err) {
        setError('ロールの削除に失敗しました');
        console.error('Error deleting role:', err);
      }
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>{t.loading}</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', p: 2 }}>
      {/* ヘッダー */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">{t.title}</Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={fetchRoles}
            sx={{ mr: 1 }}
          >
            {t.refresh}
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => handleOpenDialog()}
          >
            {t.addRole}
          </Button>
        </Box>
      </Box>

      {/* エラー表示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* ロールテーブル */}
      <TableContainer component={Paper} sx={{ flex: 1, overflow: 'auto' }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>{t.name}</TableCell>
              <TableCell>{t.displayName}</TableCell>
              <TableCell>{t.description}</TableCell>
              <TableCell>{t.userCount}</TableCell>
              <TableCell>{t.actions}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {roles.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  <Typography color="text.secondary">{t.noRoles}</Typography>
                </TableCell>
              </TableRow>
            ) : (
              roles.map((role) => (
                <TableRow key={role.id} hover>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {role.name}
                      <Chip
                        label={role.isSystemRole ? t.systemRole : t.customRole}
                        size="small"
                        color={role.isSystemRole ? 'primary' : 'default'}
                        variant="outlined"
                      />
                    </Box>
                  </TableCell>
                  <TableCell>{role.displayName}</TableCell>
                  <TableCell>{role.description}</TableCell>
                  <TableCell>{role.userCount}</TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(role)}
                      title={t.edit}
                    >
                      <Edit />
                    </IconButton>
                    {!role.isSystemRole && (
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteRole(role.id, role.isSystemRole)}
                        title={t.delete}
                        color="error"
                      >
                        <Delete />
                      </IconButton>
                    )}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* ロール編集ダイアログ */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingRole ? t.editRole : t.addNewRole}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            <TextField
              label={t.name}
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              fullWidth
              required
              disabled={editingRole?.isSystemRole}
            />
            <TextField
              label={t.displayName}
              value={formData.displayName}
              onChange={(e) => setFormData({ ...formData, displayName: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label={t.description}
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              fullWidth
              multiline
              rows={2}
            />
            
            {/* 権限設定 */}
            <Typography variant="h6" sx={{ mt: 2 }}>{t.permissions}</Typography>
            {Object.entries(permissionCategories).map(([categoryKey, category]) => (
              <Accordion key={categoryKey}>
                <AccordionSummary expandIcon={<ExpandMore />}>
                  <Typography variant="subtitle1">{category.label}</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                    {Object.entries(category.permissions).map(([permKey, permLabel]) => (
                      <FormControlLabel
                        key={permKey}
                        control={
                          <Checkbox
                            checked={formData.permissions[permKey] || false}
                            onChange={(e) => handlePermissionChange(permKey, e.target.checked)}
                          />
                        }
                        label={permLabel}
                      />
                    ))}
                  </Box>
                </AccordionDetails>
              </Accordion>
            ))}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>{t.cancel}</Button>
          <Button onClick={handleSaveRole} variant="contained">
            {t.save}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default RoleManagement;
