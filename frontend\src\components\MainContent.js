// import React from 'react';
import TranscriptCleanerContent from './TranscriptCleanerContent';
import AdminPanelContent from './AdminPanelContent';

function MainContent({
  originalText,
  correctedText,
  setCorrectedText,
  isProcessing,
  handleCorrectText,
  handleDownload,
  processingInfo,
  error,
  language,
  selectedMenu,
  userRole,
  adminSubMenu
}) {
  // 選択されたメニューに応じてコンテンツを表示
  switch (selectedMenu) {
    case 'admin':
      return (
        <AdminPanelContent
          language={language}
          userRole={userRole}
          adminSubMenu={adminSubMenu}
        />
      );
    case 'transcript':
    default:
      return (
        <TranscriptCleanerContent
          originalText={originalText}
          correctedText={correctedText}
          setCorrectedText={setCorrectedText}
          isProcessing={isProcessing}
          handleCorrectText={handleCorrectText}
          handleDownload={handleDownload}
          processingInfo={processingInfo}
          error={error}
          language={language}
        />
      );
  }
}

export default MainContent;
