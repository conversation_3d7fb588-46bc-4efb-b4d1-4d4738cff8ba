@@ -1,19 +1,120 @@
 # Code Style & Quality Rules
 
 ## Naming Conventions
+ - Use PascalCase for React components and Java classes (e.g., MainContent.js, ApiController.java)
+ - Use camelCase for variables and functions in both JavaScript and Java
+ - Use UPPER_SNAKE_CASE for constants
+ - Use kebab-case for file names in frontend (e.g., api-service.js)
+ - Prefix interface names with 'I' in Java (e.g., IConfigService)
+ - Use plural names for collections (e.g., wordLists, errorMessages)
 
 ## Code Formatting
+ - Use 2 spaces for indentation in frontend code
+ - Use 4 spaces for indentation in backend code
+ - Maximum line length: 120 characters
+ - Place opening braces on the same line
+ - Add spaces around operators
+ - No trailing whitespace
+ - Use semicolons in JavaScript
+ - Group imports by type and sort alphabetically
+ - Add empty line between logical code blocks
 
 ## Comments
+ - Use JSDoc format for documenting components and functions
+ - Add comments for complex logic or business rules
+ - Keep comments up to date with code changes
+ - Use TODO comments for future improvements
+ - Document API endpoints with clear descriptions
+ - Include parameter descriptions and return types
 
 ## Error Handling
+ - Use try-catch blocks for error-prone operations
+ - Log errors with appropriate severity levels
+ - Return meaningful error messages to users
+ - Handle API errors gracefully in frontend
+ - Implement proper error boundaries in React
+ - Use custom error types in Java when appropriate
 
 # Architecture Patterns
 
 ## Frontend
+ - Follow container/presentational component pattern
+ - Keep components small and focused
+ - Use custom hooks for reusable logic
+ - Implement proper state management
+ - Follow React best practices for performance
+ - Separate API calls into service layer
 
 ## Backend
+ - Follow layered architecture (Controller -> Service -> Repository)
+ - Use dependency injection
+ - Keep services focused on single responsibility
+ - Implement proper validation
+ - Use DTOs for data transfer
+ - Follow SOLID principles
 
 ## API Design
+ - Use RESTful conventions
+ - Version APIs appropriately
+ - Use proper HTTP methods and status codes
+ - Implement proper request/response validation
+ - Document APIs using OpenAPI/Swagger
+ - Use consistent response formats
 
 # Testing & Documentation
 
 ## Testing Requirements
+ - Maintain minimum 80% code coverage
+ - Write unit tests for business logic
+ - Implement integration tests for APIs
+ - Use meaningful test descriptions
+ - Mock external dependencies
+ - Test error scenarios
 
 ## Documentation
+ - Maintain up-to-date README
+ - Document setup and deployment procedures
+ - Include API documentation
+ - Document configuration options
+ - Add inline documentation for complex logic
+ - Keep change log updated
 
 # Security Rules
 
 ## API Authentication
+ - Implement proper API key validation
+ - Use secure headers
+ - Implement rate limiting
+ - Validate all inputs
+ - Use HTTPS only
+ - Implement proper session management
 
 ## Data Processing
+ - Sanitize user inputs
+ - Validate file uploads
+ - Implement proper error handling
+ - Use secure parsing methods
+ - Handle sensitive data appropriately
+ - Implement proper logging (no sensitive data)
