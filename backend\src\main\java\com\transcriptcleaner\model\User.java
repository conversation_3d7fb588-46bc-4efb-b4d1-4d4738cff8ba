package com.transcriptcleaner.model;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.time.LocalDateTime;
import java.util.Set;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;

@Entity
@Table(name = "user")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false, unique = true)
    private String username;

    @Column(name = "password_hash", nullable = false)
    private String passwordHash;

    @Column(nullable = false)
    private String email;

    @Column(nullable = false, columnDefinition = "datetime default current_timestamp")
    private LocalDateTime createdAt;

    @Column(nullable = false, columnDefinition = "datetime default current_timestamp on update current_timestamp")
    private LocalDateTime updatedAt;

    @Column(nullable = false, columnDefinition = "tinyint(1) default 1")
    private boolean isActive;

    // ユーザーロール関連（双方向関係）
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    @Builder.Default
    private Set<UserRole> userRoles = new HashSet<>();

    // ロール関連のヘルパーメソッド
    public Set<Role> getRoles() {
        return userRoles.stream()
                .filter(UserRole::isCurrentlyActive)
                .map(UserRole::getRole)
                .collect(Collectors.toSet());
    }

    public List<String> getRoleNames() {
        return getRoles().stream()
                .map(Role::getName)
                .collect(Collectors.toList());
    }

    public boolean hasRole(String roleName) {
        return getRoles().stream()
                .anyMatch(role -> role.getName().equals(roleName));
    }

    public boolean hasPermission(String permission) {
        return getRoles().stream()
                .anyMatch(role -> role.hasPermission(permission));
    }

    public boolean isAdmin() {
        return hasRole(Role.ADMIN);
    }

    public boolean isPremiumUser() {
        return hasRole(Role.PREMIUM);
    }

    public boolean isGuestUser() {
        return hasRole(Role.GUEST);
    }

    // JPA ライフサイクルコールバック
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
        if (updatedAt == null) {
            updatedAt = LocalDateTime.now();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    public String getPasswordHash() {
        return passwordHash;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    // toString メソッドのオーバーライド（パスワードハッシュを除外）
    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", isActive=" + isActive +
                ", createdAt=" + createdAt +
                '}';
    }

}