.app-container {
  padding: 16px;
}

/* Material-UI Paperの高さを100%に設定 */
.MuiPaper-root {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Diff表示のスタイル */
.diff-added {
  background-color: #e6ffed;
}

.diff-removed {
  background-color: #ffeef0;
}

/* ファイルアップロードボタンのスタイル */
.file-upload-button {
  text-transform: none;
}

/* ローディングインジケーターのスタイル */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 20px 0;
}

/* APIキー警告のスタイル */
.api-key-warning {
  background-color: #fff3e0;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 16px;
}

/* コスト情報のスタイル */
.cost-info {
  font-size: 0.8rem;
  color: #666;
  margin-top: 8px;
  text-align: right;
}