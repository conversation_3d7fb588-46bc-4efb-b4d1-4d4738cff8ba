package com.transcriptcleaner.controller;

import com.transcriptcleaner.model.*;
import com.transcriptcleaner.service.ConfigService;
import com.transcriptcleaner.service.OpenAiService;
import com.transcriptcleaner.service.CsvService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api")
@RequiredArgsConstructor
@Slf4j
public class ApiController {

    private final OpenAiService openAiService;
    private final ConfigService configService;
    private final CsvService csvService;

    @GetMapping("/check-api-key")
    public ResponseEntity<ApiKeyResponse> checkApiKey() {
        String apiKey = configService.getApiKey();
        boolean isValid = apiKey != null && !apiKey.isEmpty() && openAiService.isApiKeyValid(apiKey);
        
        ApiKeyResponse response = ApiKeyResponse.builder()
                .valid(isValid)
                .message(isValid ? "APIキーは有効です" : "APIキーが無効または設定されていません")
                .totalCost(configService.getTotalCost())
                .build();
        
        return ResponseEntity.ok(response);
    }



    @PostMapping("/correct-text")
    public ResponseEntity<CorrectionResponse> correctText(@RequestBody CorrectionRequest request) {
        CorrectionResponse response = openAiService.correctText(request);
        
        if (response.getError() != null) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
        
        return ResponseEntity.ok(response);
    }

    @PostMapping("/upload-csv")
    public ResponseEntity<String> uploadCsv(@RequestParam("file") MultipartFile file) {
        try {
            String text = csvService.parseCsvFile(file);
            return ResponseEntity.ok(text);
        } catch (IOException e) {
            log.error("CSVファイルのアップロードに失敗しました", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("CSVファイルの処理に失敗しました: " + e.getMessage());
        }
    }

    @GetMapping("/download-csv")
    public ResponseEntity<byte[]> downloadCsv(
            @RequestParam("original") String originalText,
            @RequestParam("corrected") String correctedText) {
        try {
            byte[] csvData = csvService.generateCsvFile(originalText, correctedText);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "corrected_text.csv");
            
            return new ResponseEntity<>(csvData, headers, HttpStatus.OK);
        } catch (IOException e) {
            log.error("CSVファイルの生成に失敗しました", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    @PostMapping("/reset-cost")
    public ResponseEntity<ApiKeyResponse> resetCost() {
        configService.resetCost();
        
        return ResponseEntity.ok(ApiKeyResponse.builder()
                .valid(true)
                .message("コスト履歴をリセットしました")
                .totalCost(0.0)
                .build());
    }

    @PostMapping("/save-word-list")
    public ResponseEntity<WordListInfo> saveWordList(@RequestBody WordListRequest request) {
        configService.saveWordList(request.getName(), request.getWordList());
        
        WordListInfo wordListInfo = configService.getWordLists().stream()
                .filter(wl -> wl.getName().equals(request.getName()))
                .findFirst()
                .orElse(null);
        
        return ResponseEntity.ok(wordListInfo);
    }

    @GetMapping("/word-lists")
    public ResponseEntity<List<WordListInfo>> getWordLists() {
        return ResponseEntity.ok(configService.getWordLists());
    }

    @DeleteMapping("/word-list/{name}")
    public ResponseEntity<Void> deleteWordList(@PathVariable String name) {
        boolean deleted = configService.deleteWordList(name);
        
        if (deleted) {
            return ResponseEntity.ok().build();
        } else {
            return ResponseEntity.notFound().build();
        }
    }
}