package com.transcriptcleaner.controller;

import com.transcriptcleaner.model.Role;
import com.transcriptcleaner.model.UserRole;
import com.transcriptcleaner.service.RoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/roles")
@RequiredArgsConstructor
@Slf4j
public class RoleController {
    
    private final RoleService roleService;

    /**
     * 全てのアクティブなロールを取得
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('USER')")
    public ResponseEntity<List<Role>> getAllRoles() {
        try {
            List<Role> roles = roleService.getAllActiveRoles();
            return ResponseEntity.ok(roles);
        } catch (Exception e) {
            log.error("ロール一覧取得エラー", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * ロールIDでロールを取得
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Role> getRoleById(@PathVariable Long id) {
        try {
            return roleService.getRoleById(id)
                    .map(role -> ResponseEntity.ok(role))
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            log.error("ロール取得エラー: ID={}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 新しいロールを作成
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Role> createRole(@RequestBody Map<String, String> request) {
        try {
            String name = request.get("name");
            String displayName = request.get("displayName");
            String description = request.get("description");
            String permissions = request.get("permissions");

            if (name == null || name.trim().isEmpty()) {
                return ResponseEntity.badRequest().build();
            }

            Role role = roleService.createRole(name, displayName, description, permissions);
            return ResponseEntity.status(HttpStatus.CREATED).body(role);
        } catch (IllegalArgumentException e) {
            log.warn("ロール作成エラー: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("ロール作成エラー", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * ロールを更新
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Role> updateRole(@PathVariable Long id, @RequestBody Map<String, String> request) {
        try {
            String displayName = request.get("displayName");
            String description = request.get("description");
            String permissions = request.get("permissions");

            Role role = roleService.updateRole(id, displayName, description, permissions);
            return ResponseEntity.ok(role);
        } catch (IllegalArgumentException e) {
            log.warn("ロール更新エラー: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("ロール更新エラー: ID={}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * ロールを無効化
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> deactivateRole(@PathVariable Long id) {
        try {
            roleService.deactivateRole(id);
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            log.warn("ロール削除エラー: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("ロール削除エラー: ID={}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * ユーザーにロールを割り当て
     */
    @PostMapping("/assign")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<UserRole> assignRoleToUser(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            Long roleId = Long.valueOf(request.get("roleId").toString());
            Long assignedByUserId = Long.valueOf(request.get("assignedByUserId").toString());
            
            LocalDateTime expiresAt = null;
            if (request.get("expiresAt") != null) {
                expiresAt = LocalDateTime.parse(request.get("expiresAt").toString());
            }

            UserRole userRole = roleService.assignRoleToUser(userId, roleId, assignedByUserId, expiresAt);
            return ResponseEntity.status(HttpStatus.CREATED).body(userRole);
        } catch (IllegalArgumentException e) {
            log.warn("ロール割り当てエラー: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("ロール割り当てエラー", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * ユーザーからロールを削除
     */
    @DeleteMapping("/remove")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Void> removeRoleFromUser(@RequestBody Map<String, Object> request) {
        try {
            Long userId = Long.valueOf(request.get("userId").toString());
            Long roleId = Long.valueOf(request.get("roleId").toString());

            roleService.removeRoleFromUser(userId, roleId);
            return ResponseEntity.noContent().build();
        } catch (IllegalArgumentException e) {
            log.warn("ロール削除エラー: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            log.error("ロール削除エラー", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * ユーザーのアクティブなロールを取得
     */
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasRole('ADMIN') or #userId == authentication.principal.id")
    public ResponseEntity<List<UserRole>> getUserRoles(@PathVariable Long userId) {
        try {
            List<UserRole> userRoles = roleService.getUserActiveRoles(userId);
            return ResponseEntity.ok(userRoles);
        } catch (Exception e) {
            log.error("ユーザーロール取得エラー: ユーザーID={}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * ユーザーが特定のロールを持っているかチェック
     */
    @GetMapping("/check/{userId}/{roleName}")
    @PreAuthorize("hasRole('ADMIN') or #userId == authentication.principal.id")
    public ResponseEntity<Map<String, Boolean>> checkUserRole(@PathVariable Long userId, @PathVariable String roleName) {
        try {
            boolean hasRole = roleService.hasUserRole(userId, roleName);
            return ResponseEntity.ok(Map.of("hasRole", hasRole));
        } catch (Exception e) {
            log.error("ユーザーロールチェックエラー: ユーザーID={}, ロール={}", userId, roleName, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * ユーザーが特定の権限を持っているかチェック
     */
    @GetMapping("/permission/{userId}/{permission}")
    @PreAuthorize("hasRole('ADMIN') or #userId == authentication.principal.id")
    public ResponseEntity<Map<String, Boolean>> checkUserPermission(@PathVariable Long userId, @PathVariable String permission) {
        try {
            boolean hasPermission = roleService.hasUserPermission(userId, permission);
            return ResponseEntity.ok(Map.of("hasPermission", hasPermission));
        } catch (Exception e) {
            log.error("ユーザー権限チェックエラー: ユーザーID={}, 権限={}", userId, permission, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 期限切れロールのクリーンアップ
     */
    @PostMapping("/cleanup")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Integer>> cleanupExpiredRoles() {
        try {
            int cleanedCount = roleService.cleanupExpiredRoles();
            return ResponseEntity.ok(Map.of("cleanedCount", cleanedCount));
        } catch (Exception e) {
            log.error("期限切れロールクリーンアップエラー", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * ロール統計情報を取得
     */
    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Map<String, Object>> getRoleStatistics() {
        try {
            List<Object[]> roleStats = roleService.getRoleStatistics();
            List<Object[]> userStats = roleService.getUserStatistics();
            
            return ResponseEntity.ok(Map.of(
                "roleStatistics", roleStats,
                "userStatistics", userStats
            ));
        } catch (Exception e) {
            log.error("ロール統計取得エラー", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 期限切れ予定のロールを取得
     */
    @GetMapping("/expiring/{days}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<List<UserRole>> getExpiringRoles(@PathVariable int days) {
        try {
            List<UserRole> expiringRoles = roleService.getRolesExpiringWithinDays(days);
            return ResponseEntity.ok(expiringRoles);
        } catch (Exception e) {
            log.error("期限切れ予定ロール取得エラー: 日数={}", days, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}
