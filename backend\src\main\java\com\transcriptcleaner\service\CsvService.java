package com.transcriptcleaner.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.CSVRecord;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class CsvService {

    public String parseCsvFile(MultipartFile file) throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8));
             CSVParser csvParser = new CSVParser(reader,
                     CSVFormat.DEFAULT.builder()
                             .setHeader()
                             .setIgnoreHeaderCase(true)
                             .setTrim(true)
                             .build())) {

            StringBuilder textBuilder = new StringBuilder();
            for (CSVRecord record : csvParser) {
                // Microsoft Teamsの文字起こしCSVフォーマットに合わせて処理
                // 通常、「名前」「テキスト」「開始時間」「終了時間」のような列がある
                String name = record.size() > 0 ? record.get(0) : "";
                String text = record.size() > 1 ? record.get(1) : "";

                // どちらかが空の場合はスキップも可（必要に応じて）
                if (name.isEmpty() && text.isEmpty()) continue;

                textBuilder.append(name).append(": ").append(text).append("\n");
            }

            return textBuilder.toString();
        } catch (Exception e) {
            log.error("CSVファイルの解析に失敗しました", e);
            throw new IOException("CSVファイルの解析に失敗しました: " + e.getMessage(), e);
        }
    }

    public byte[] generateCsvFile(String originalText, String correctedText) throws IOException {
        // 元のテキストと修正されたテキストを行ごとに分割
        String[] originalLines = originalText.split("\\n");
        String[] correctedLines = correctedText.split("\\n");

        StringWriter writer = new StringWriter();
        try (CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.builder()
                .setHeader("Original", "Corrected")
                .build())) {

            int maxLines = Math.max(originalLines.length, correctedLines.length);
            for (int i = 0; i < maxLines; i++) {
                String original = i < originalLines.length ? originalLines[i] : "";
                String corrected = i < correctedLines.length ? correctedLines[i] : "";

                csvPrinter.printRecord(original, corrected);
            }
        }

        return writer.toString().getBytes(StandardCharsets.UTF_8);
    }

    public List<String[]> parseWordList(String wordListCsv) {
        List<String[]> result = new ArrayList<>();
        if (wordListCsv == null || wordListCsv.isEmpty()) {
            return result;
        }

        String[] lines = wordListCsv.split("\\n");
        for (String line : lines) {
            String[] parts = line.split(",");
            if (parts.length >= 2) {
                result.add(new String[]{parts[0].trim(), parts[1].trim()});
            }
        }

        return result;
    }
}
