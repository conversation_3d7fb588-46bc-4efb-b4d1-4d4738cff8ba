package com.transcriptcleaner.repository;

import com.transcriptcleaner.model.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long> {
    
    /**
     * ロール名でロールを検索
     */
    Optional<Role> findByName(String name);
    
    /**
     * アクティブなロールのみを取得
     */
    List<Role> findByIsActiveTrue();
    
    /**
     * ロール名でアクティブなロールを検索
     */
    Optional<Role> findByNameAndIsActiveTrue(String name);
    
    /**
     * 表示名でロールを検索
     */
    Optional<Role> findByDisplayName(String displayName);
    
    /**
     * 表示名の部分一致でロールを検索
     */
    List<Role> findByDisplayNameContainingIgnoreCase(String displayName);
    
    /**
     * システムロール（ADMIN, USER, GUEST）以外のロールを取得
     */
    @Query("SELECT r FROM Role r WHERE r.name NOT IN ('ADMIN', 'USER', 'GUEST') AND r.isActive = true")
    List<Role> findCustomRoles();
    
    /**
     * 特定の権限を持つロールを検索
     */
    @Query("SELECT r FROM Role r WHERE r.permissions LIKE %:permission% AND r.isActive = true")
    List<Role> findByPermission(@Param("permission") String permission);
    
    /**
     * ユーザー数でソートされたロール一覧を取得
     */
    @Query("SELECT r, COUNT(ur.id) as userCount " +
           "FROM Role r LEFT JOIN r.userRoles ur " +
           "WHERE r.isActive = true AND (ur.isActive = true OR ur.isActive IS NULL) " +
           "GROUP BY r.id " +
           "ORDER BY userCount DESC")
    List<Object[]> findRolesWithUserCount();
    
    /**
     * ロール名の存在チェック（大文字小文字を区別しない）
     */
    boolean existsByNameIgnoreCase(String name);
    
    /**
     * 表示名の存在チェック（大文字小文字を区別しない）
     */
    boolean existsByDisplayNameIgnoreCase(String displayName);
}
