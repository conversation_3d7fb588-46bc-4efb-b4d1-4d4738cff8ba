package com.transcriptcleaner.repository;

import com.transcriptcleaner.model.UserRole;
import com.transcriptcleaner.model.User;
import com.transcriptcleaner.model.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRoleRepository extends JpaRepository<UserRole, Long> {
    
    /**
     * ユーザーのアクティブなロール割り当てを取得
     */
    List<UserRole> findByUserAndIsActiveTrueOrderByAssignedAtDesc(User user);
    
    /**
     * ユーザーIDでアクティブなロール割り当てを取得
     */
    @Query("SELECT ur FROM UserRole ur WHERE ur.user.id = :userId AND ur.isActive = true " +
           "AND (ur.expiresAt IS NULL OR ur.expiresAt > CURRENT_TIMESTAMP)")
    List<UserRole> findActiveRolesByUserId(@Param("userId") Long userId);
    
    /**
     * ロールのアクティブなユーザー割り当てを取得
     */
    List<UserRole> findByRoleAndIsActiveTrueOrderByAssignedAtDesc(Role role);
    
    /**
     * ユーザーとロールの組み合わせでアクティブな割り当てを検索
     */
    Optional<UserRole> findByUserAndRoleAndIsActiveTrue(User user, Role role);
    
    /**
     * ユーザーが特定のロールを持っているかチェック
     */
    @Query("SELECT COUNT(ur) > 0 FROM UserRole ur WHERE ur.user.id = :userId AND ur.role.name = :roleName " +
           "AND ur.isActive = true AND (ur.expiresAt IS NULL OR ur.expiresAt > CURRENT_TIMESTAMP)")
    boolean hasUserRole(@Param("userId") Long userId, @Param("roleName") String roleName);
    
    /**
     * 期限切れのロール割り当てを取得
     */
    @Query("SELECT ur FROM UserRole ur WHERE ur.expiresAt IS NOT NULL AND ur.expiresAt < CURRENT_TIMESTAMP " +
           "AND ur.isActive = true")
    List<UserRole> findExpiredRoles();
    
    /**
     * 期限切れのロール割り当てを無効化
     */
    @Modifying
    @Transactional
    @Query("UPDATE UserRole ur SET ur.isActive = false WHERE ur.expiresAt IS NOT NULL " +
           "AND ur.expiresAt < CURRENT_TIMESTAMP AND ur.isActive = true")
    int deactivateExpiredRoles();
    
    /**
     * ユーザーの特定ロール割り当てを無効化
     */
    @Modifying
    @Transactional
    @Query("UPDATE UserRole ur SET ur.isActive = false WHERE ur.user.id = :userId AND ur.role.id = :roleId")
    int deactivateUserRole(@Param("userId") Long userId, @Param("roleId") Long roleId);
    
    /**
     * 指定日数以内に期限切れになるロール割り当てを取得
     */
    @Query("SELECT ur FROM UserRole ur WHERE ur.expiresAt IS NOT NULL " +
           "AND ur.expiresAt BETWEEN CURRENT_TIMESTAMP AND :expiryDate AND ur.isActive = true")
    List<UserRole> findRolesExpiringBefore(@Param("expiryDate") LocalDateTime expiryDate);
    
    /**
     * ユーザー別ロール統計を取得
     */
    @Query("SELECT ur.user.id, ur.user.username, COUNT(ur.id) as roleCount " +
           "FROM UserRole ur WHERE ur.isActive = true " +
           "AND (ur.expiresAt IS NULL OR ur.expiresAt > CURRENT_TIMESTAMP) " +
           "GROUP BY ur.user.id, ur.user.username " +
           "ORDER BY roleCount DESC")
    List<Object[]> getUserRoleStatistics();
    
    /**
     * ロール別ユーザー統計を取得
     */
    @Query("SELECT ur.role.id, ur.role.name, ur.role.displayName, COUNT(ur.id) as userCount " +
           "FROM UserRole ur WHERE ur.isActive = true " +
           "AND (ur.expiresAt IS NULL OR ur.expiresAt > CURRENT_TIMESTAMP) " +
           "GROUP BY ur.role.id, ur.role.name, ur.role.displayName " +
           "ORDER BY userCount DESC")
    List<Object[]> getRoleUserStatistics();
    
    /**
     * 特定のユーザーによって割り当てられたロールを取得
     */
    List<UserRole> findByAssignedByAndIsActiveTrueOrderByAssignedAtDesc(User assignedBy);
    
    /**
     * ユーザーのロール履歴を取得（無効化されたものも含む）
     */
    List<UserRole> findByUserOrderByAssignedAtDesc(User user);
    
    /**
     * 一定期間内に割り当てられたロールを取得
     */
    @Query("SELECT ur FROM UserRole ur WHERE ur.assignedAt BETWEEN :startDate AND :endDate " +
           "ORDER BY ur.assignedAt DESC")
    List<UserRole> findRoleAssignmentsBetween(@Param("startDate") LocalDateTime startDate, 
                                             @Param("endDate") LocalDateTime endDate);
}
