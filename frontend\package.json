{"name": "transcript-cleaner-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/system": "^7.1.0", "@mui/x-data-grid": "^7.1.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.0", "diff": "^5.1.0", "file-saver": "^2.0.5", "react": "^18.2.0", "react-diff-view": "^3.3.1", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "reselect": "^5.1.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8080"}