import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Edit,
  Delete,
  Add,
  Refresh,
} from '@mui/icons-material';

function UserManagement({ language }) {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [openDialog, setOpenDialog] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    isActive: true
  });

  // ラベル定義
  const labels = {
    ja: {
      title: 'ユーザー管理',
      addUser: 'ユーザー追加',
      refresh: '更新',
      username: 'ユーザー名',
      email: 'メールアドレス',
      password: 'パスワード',
      status: 'ステータス',
      roles: 'ロール',
      actions: '操作',
      active: 'アクティブ',
      inactive: '非アクティブ',
      edit: '編集',
      delete: '削除',
      save: '保存',
      cancel: 'キャンセル',
      addNewUser: '新規ユーザー追加',
      editUser: 'ユーザー編集',
      confirmDelete: 'このユーザーを削除しますか？',
      loading: '読み込み中...',
      noUsers: 'ユーザーが見つかりません',
    },
    en: {
      title: 'User Management',
      addUser: 'Add User',
      refresh: 'Refresh',
      username: 'Username',
      email: 'Email',
      password: 'Password',
      status: 'Status',
      roles: 'Roles',
      actions: 'Actions',
      active: 'Active',
      inactive: 'Inactive',
      edit: 'Edit',
      delete: 'Delete',
      save: 'Save',
      cancel: 'Cancel',
      addNewUser: 'Add New User',
      editUser: 'Edit User',
      confirmDelete: 'Are you sure you want to delete this user?',
      loading: 'Loading...',
      noUsers: 'No users found',
    }
  };
  const t = labels[language] || labels['ja'];

  // ユーザー一覧を取得
  const fetchUsers = async () => {
    setLoading(true);
    try {
      // TODO: 実際のAPI呼び出しに置き換え
      // const response = await fetch('/api/users');
      // const data = await response.json();
      
      // モックデータ
      const mockUsers = [
        {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          isActive: true,
          roles: ['ADMIN'],
          createdAt: '2024-01-01T00:00:00Z'
        },
        {
          id: 2,
          username: 'testuser',
          email: '<EMAIL>',
          isActive: true,
          roles: ['USER'],
          createdAt: '2024-01-02T00:00:00Z'
        },
        {
          id: 3,
          username: 'demo',
          email: '<EMAIL>',
          isActive: false,
          roles: ['GUEST'],
          createdAt: '2024-01-03T00:00:00Z'
        }
      ];
      
      setUsers(mockUsers);
      setError('');
    } catch (err) {
      setError('ユーザー一覧の取得に失敗しました');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  // 初期データ読み込み
  useEffect(() => {
    fetchUsers();
  }, []);

  // ダイアログを開く
  const handleOpenDialog = (user = null) => {
    if (user) {
      setEditingUser(user);
      setFormData({
        username: user.username,
        email: user.email,
        password: '',
        isActive: user.isActive
      });
    } else {
      setEditingUser(null);
      setFormData({
        username: '',
        email: '',
        password: '',
        isActive: true
      });
    }
    setOpenDialog(true);
  };

  // ダイアログを閉じる
  const handleCloseDialog = () => {
    setOpenDialog(false);
    setEditingUser(null);
    setFormData({
      username: '',
      email: '',
      password: '',
      isActive: true
    });
  };

  // ユーザー保存
  const handleSaveUser = async () => {
    try {
      // TODO: 実際のAPI呼び出しに置き換え
      if (editingUser) {
        // 更新
        console.log('Updating user:', editingUser.id, formData);
      } else {
        // 新規作成
        console.log('Creating user:', formData);
      }
      
      handleCloseDialog();
      fetchUsers(); // リストを更新
    } catch (err) {
      setError('ユーザーの保存に失敗しました');
      console.error('Error saving user:', err);
    }
  };

  // ユーザー削除
  const handleDeleteUser = async (userId) => {
    if (window.confirm(t.confirmDelete)) {
      try {
        // TODO: 実際のAPI呼び出しに置き換え
        console.log('Deleting user:', userId);
        fetchUsers(); // リストを更新
      } catch (err) {
        setError('ユーザーの削除に失敗しました');
        console.error('Error deleting user:', err);
      }
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>{t.loading}</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', p: 2 }}>
      {/* ヘッダー */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">{t.title}</Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={fetchUsers}
            sx={{ mr: 1 }}
          >
            {t.refresh}
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => handleOpenDialog()}
          >
            {t.addUser}
          </Button>
        </Box>
      </Box>

      {/* エラー表示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* ユーザーテーブル */}
      <TableContainer component={Paper} sx={{ flex: 1, overflow: 'auto' }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>{t.username}</TableCell>
              <TableCell>{t.email}</TableCell>
              <TableCell>{t.status}</TableCell>
              <TableCell>{t.roles}</TableCell>
              <TableCell>{t.actions}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  <Typography color="text.secondary">{t.noUsers}</Typography>
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <TableRow key={user.id} hover>
                  <TableCell>{user.username}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Chip
                      label={user.isActive ? t.active : t.inactive}
                      color={user.isActive ? 'success' : 'default'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {user.roles.map((role) => (
                      <Chip
                        key={role}
                        label={role}
                        variant="outlined"
                        size="small"
                        sx={{ mr: 0.5 }}
                      />
                    ))}
                  </TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(user)}
                      title={t.edit}
                    >
                      <Edit />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteUser(user.id)}
                      title={t.delete}
                      color="error"
                    >
                      <Delete />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {/* ユーザー編集ダイアログ */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingUser ? t.editUser : t.addNewUser}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, pt: 1 }}>
            <TextField
              label={t.username}
              value={formData.username}
              onChange={(e) => setFormData({ ...formData, username: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label={t.email}
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label={t.password}
              type="password"
              value={formData.password}
              onChange={(e) => setFormData({ ...formData, password: e.target.value })}
              fullWidth
              required={!editingUser}
              helperText={editingUser ? '空白の場合はパスワードを変更しません' : ''}
            />
            <FormControl fullWidth>
              <InputLabel>{t.status}</InputLabel>
              <Select
                value={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.value })}
                label={t.status}
              >
                <MenuItem value={true}>{t.active}</MenuItem>
                <MenuItem value={false}>{t.inactive}</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>{t.cancel}</Button>
          <Button onClick={handleSaveUser} variant="contained">
            {t.save}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default UserManagement;
