import React from 'react';
import { Box, Typography } from '@mui/material';
import UserManagement from './UserManagement';
import RoleManagement from './RoleManagement';

function AdminPanelContent({ language, userRole, adminSubMenu }) {
  // 権限チェック
  if (userRole !== 'ADMIN') {
    return (
      <Box sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100%',
        p: 3 
      }}>
        <Typography variant="h6" color="text.secondary">
          {language === 'ja' ? 'この機能にアクセスする権限がありません。' : 'You do not have permission to access this feature.'}
        </Typography>
      </Box>
    );
  }

  // サブメニューに応じたコンテンツを表示
  const renderContent = () => {
    switch (adminSubMenu) {
      case 'users':
        return (
          <Box sx={{ height: '100%', overflow: 'hidden' }}>
            <UserManagement language={language} />
          </Box>
        );
      case 'roles':
        return (
          <Box sx={{ height: '100%', overflow: 'hidden' }}>
            <RoleManagement language={language} />
          </Box>
        );
      case 'statistics':
        return (
          <Box sx={{
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Typography variant="h6" color="text.secondary">
              📊 {language === 'ja' ? '統計情報（開発中）' : 'Statistics (Coming Soon)'}
            </Typography>
          </Box>
        );
      default:
        // デフォルトは上下分割表示
        return (
          <>
            {/* 上半分: ユーザー管理 */}
            <Box sx={{
              flex: '1 1 50%',
              borderRadius: 2,
              border: '1px solid #e0e0e0',
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column'
            }}>
              <Box sx={{
                p: 2,
                backgroundColor: '#f5f6fa',
                borderBottom: '1px solid #e0e0e0'
              }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  👥 {language === 'ja' ? 'ユーザー管理' : 'User Management'}
                </Typography>
              </Box>
              <Box sx={{ flex: 1, overflow: 'hidden' }}>
                <UserManagement language={language} />
              </Box>
            </Box>

            {/* 下半分: ロール管理 */}
            <Box sx={{
              flex: '1 1 50%',
              borderRadius: 2,
              border: '1px solid #e0e0e0',
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'column'
            }}>
              <Box sx={{
                p: 2,
                backgroundColor: '#f5f6fa',
                borderBottom: '1px solid #e0e0e0'
              }}>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  🔐 {language === 'ja' ? 'ロール管理' : 'Role Management'}
                </Typography>
              </Box>
              <Box sx={{ flex: 1, overflow: 'hidden' }}>
                <RoleManagement language={language} />
              </Box>
            </Box>
          </>
        );
    }
  };

  return (
    <Box sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      p: 2,
      gap: 2
    }}>
      {/* ヘッダー */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, color: 'primary.main' }}>
          {language === 'ja' ? '管理機能' : 'Administration'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          {language === 'ja'
            ? 'ユーザーとロールの管理を行います。'
            : 'Manage users and roles.'}
        </Typography>
      </Box>

      {/* コンテンツエリア */}
      <Box sx={{
        flex: 1,
        display: 'flex',
        flexDirection: adminSubMenu === 'users' || adminSubMenu === 'roles' || adminSubMenu === 'statistics' ? 'column' : 'column',
        gap: 2
      }}>
        {renderContent()}
      </Box>
    </Box>
  );
}

export default AdminPanelContent;
